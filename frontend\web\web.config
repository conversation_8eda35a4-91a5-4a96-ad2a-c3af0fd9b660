<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <directoryBrowse enabled="false" />

        <rewrite>
            <rules>
                <rule name="Hide Yii Index" stopProcessing="true">
                    <match url="." ignoreCase="false" />
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" ignoreCase="false" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" ignoreCase="false" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="index.php" appendQueryString="true" />
                </rule>
            </rules>
        </rewrite>
       <!-- ✅ Enable all HTTP verbs (especially POST) -->
        <handlers>
            <remove name="PHP_via_FastCGI" />
            <add name="PHP_via_FastCGI"
                 path="*.php"
                 verb="*"
                 modules="FastCgiModule"
                 scriptProcessor="C:\Program Files\PHP\v8.3\php-cgi.exe"
                 resourceType="Either"
                 requireAccess="Script" />
        </handlers>
<!-- 
        <modules runAllManagedModulesForAllRequests="true" /> -->
    </system.webServer>
</configuration>
