a:14:{s:6:"config";s:12881:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:57:{s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\test-project\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\test-project\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\test-project\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\test-project\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\test-project\vendor/raoul2000/yii2-workflow/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\test-project\vendor/yiisoft/yii2-jui";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\test-project\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\test-project\vendor/cornernote/yii2-workflow-manager/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\test-project\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\test-project\vendor/kartik-v/yii2-grid/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\test-project\vendor/kartik-v/yii2-popover-x/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\test-project\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\test-project\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-editable/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\test-project\vendor/yiisoft/yii2-bootstrap4/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\test-project\vendor/hail812/yii2-adminlte-widgets/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\test-project\vendor/hail812/yii2-adminlte3/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\test-project\vendor/kartik-v/yii2-widget-activeform/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\test-project\vendor/kartik-v/yii2-mpdf/src";}}s:33:"dominus77/yii2-sweetalert2-widget";a:3:{s:4:"name";s:33:"dominus77/yii2-sweetalert2-widget";s:7:"version";s:7:"2.0.0.0";s:5:"alias";a:1:{s:22:"@dominus77/sweetalert2";s:64:"C:\Web\test-project\vendor/dominus77/yii2-sweetalert2-widget/src";}}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\test-project\vendor/kartik-v/yii2-widget-select2/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\test-project\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\test-project\vendor/yiisoft/yii2-twig/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\test-project\vendor/kartik-v/yii2-helpers/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\test-project\vendor/kartik-v/yii2-builder/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\test-project\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\test-project\vendor/kartik-v/yii2-context-menu/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\test-project\vendor/kartik-v/yii2-date-range/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\test-project\vendor/kartik-v/yii2-datecontrol/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\test-project\vendor/kartik-v/yii2-detail-view/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-sortable/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-dynagrid/src";}}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-export/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\test-project\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\test-project\vendor/yiisoft/yii2-httpclient/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\test-project\vendor/kartik-v/yii2-icons/src";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\test-project\vendor/kartik-v/yii2-label-inplace/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-markdown/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\test-project\vendor/kartik-v/yii2-money/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\test-project\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\test-project\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"1.3.2.0";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\test-project\vendor/kartik-v/yii2-slider";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-social/src";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\test-project\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-tabs-x/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\test-project\vendor/kartik-v/yii2-widget-growl/src";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\test-project\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\test-project\vendor/yiisoft/yii2-debug/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\test-project\vendor/kartik-v/yii2-widget-switchinput";}}s:18:"mdmsoft/yii2-admin";a:3:{s:4:"name";s:18:"mdmsoft/yii2-admin";s:7:"version";s:8:"2.12.0.0";s:5:"alias";a:1:{s:10:"@mdm/admin";s:45:"C:\Web\test-project\vendor/mdmsoft/yii2-admin";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\test-project\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\test-project\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:25:"webzop/yii2-notifications";a:4:{s:4:"name";s:25:"webzop/yii2-notifications";s:7:"version";s:7:"0.2.0.0";s:5:"alias";a:1:{s:21:"@webzop/notifications";s:52:"C:\Web\test-project\vendor/webzop/yii2-notifications";}s:9:"bootstrap";s:30:"webzop\notifications\Bootstrap";}s:27:"tuyakhov/yii2-notifications";a:3:{s:4:"name";s:27:"tuyakhov/yii2-notifications";s:7:"version";s:7:"*******";s:5:"alias";a:2:{s:23:"@tuyakhov/notifications";s:58:"C:\Web\test-project\vendor/tuyakhov/yii2-notifications/src";s:29:"@tuyakhov/notifications/tests";s:60:"C:\Web\test-project\vendor/tuyakhov/yii2-notifications/tests";}}}}";s:3:"log";s:6647:"a:1:{s:8:"messages";a:22:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752514304.172887;i:4;a:0:{}i:5;i:2783240;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752514304.173433;i:4;a:0:{}i:5;i:2815544;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752514304.173442;i:4;a:0:{}i:5;i:2816480;}i:3;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752514304.174018;i:4;a:0:{}i:5;i:2849728;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752514304.175028;i:4;a:0:{}i:5;i:2955032;}i:5;a:6:{i:0;s:58:"Bootstrap with webzop\notifications\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752514304.175841;i:4;a:0:{}i:5;i:3022768;}i:6;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752514304.177222;i:4;a:0:{}i:5;i:3111984;}i:7;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752514304.177233;i:4;a:0:{}i:5;i:3112624;}i:8;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752514304.185603;i:4;a:0:{}i:5;i:4340400;}i:9;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752514304.198142;i:4;a:0:{}i:5;i:4731704;}i:17;a:6:{i:0;s:28:"Route requested: 'api/index'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1752514304.20677;i:4;a:0:{}i:5;i:4954016;}i:18;a:6:{i:0;s:23:"Route to run: api/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1752514304.21274;i:4;a:0:{}i:5;i:5095336;}i:19;a:6:{i:0;s:65:"Running action: frontend\controllers\ApiController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1752514304.214534;i:4;a:0:{}i:5;i:5113448;}i:20;a:6:{i:0;s:28:"SHOW FULL COLUMNS FROM `api`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.247925;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6432112;}i:21;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=test_project";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1752514304.247984;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6434232;}i:26;a:6:{i:0;s:23:"SHOW CREATE TABLE `api`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.275996;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6492040;}i:29;a:6:{i:0;s:762:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'api' AND `kcu`.`TABLE_NAME` = 'api'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.278624;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6516360;}i:32;a:6:{i:0;s:142:"SELECT * FROM `api` WHERE (`token`='lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO') AND (`is_active`=1) AND (`name`='sync')";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.291464;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6783592;}i:35;a:6:{i:0;s:56:"UPDATE `api` SET `rate_limit_remaining`=70 WHERE `id`=13";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1752514304.293135;i:4;a:2:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:43;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:71;s:8:"function";s:16:"enforceRateLimit";s:5:"class";s:34:"frontend\controllers\ApiController";s:4:"type";s:2:"->";}}i:5;i:6859704;}i:38;a:6:{i:0;s:181:"SELECT `sync_data`.`change_id`, `sync_data`.`table_name`, `sync_data`.`pk`, `sync_data`.`action`, `sync_data`.`change_time`, `sync_data`.`synced` FROM `sync_data` WHERE `synced`='0'";i:1;i:8;i:2;s:47:"frontend\controllers\ApiController::actionIndex";i:3;d:1752514304.300343;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:112;s:8:"function";s:5:"debug";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}}i:5;i:6865208;}i:39;a:6:{i:0;s:181:"SELECT `sync_data`.`change_id`, `sync_data`.`table_name`, `sync_data`.`pk`, `sync_data`.`action`, `sync_data`.`change_time`, `sync_data`.`synced` FROM `sync_data` WHERE `synced`='0'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.300388;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:115;s:8:"function";s:3:"all";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:6869288;}i:42;a:6:{i:0;s:62:"SELECT * FROM `notification_trigger` WHERE `route`='api/index'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.302599;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:6890544;}}}";s:9:"profiling";s:8044:"a:3:{s:6:"memory";i:7109560;s:4:"time";d:0.1513049602508545;s:8:"messages";a:16:{i:22;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=test_project";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1752514304.247993;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6435040;}i:23;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=test_project";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1752514304.270901;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6478344;}i:24;a:6:{i:0;s:28:"SHOW FULL COLUMNS FROM `api`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.271096;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6478128;}i:25;a:6:{i:0;s:28:"SHOW FULL COLUMNS FROM `api`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.27591;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6490760;}i:27;a:6:{i:0;s:23:"SHOW CREATE TABLE `api`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.276018;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6492952;}i:28;a:6:{i:0;s:23:"SHOW CREATE TABLE `api`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.277203;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6494888;}i:30;a:6:{i:0;s:762:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'api' AND `kcu`.`TABLE_NAME` = 'api'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.278651;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6517400;}i:31;a:6:{i:0;s:762:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'api' AND `kcu`.`TABLE_NAME` = 'api'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.28101;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6519256;}i:33;a:6:{i:0;s:142:"SELECT * FROM `api` WHERE (`token`='lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO') AND (`is_active`=1) AND (`name`='sync')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.29152;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6785152;}i:34;a:6:{i:0;s:142:"SELECT * FROM `api` WHERE (`token`='lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO') AND (`is_active`=1) AND (`name`='sync')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.292623;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6787656;}i:36;a:6:{i:0;s:56:"UPDATE `api` SET `rate_limit_remaining`=70 WHERE `id`=13";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1752514304.293154;i:4;a:2:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:43;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:71;s:8:"function";s:16:"enforceRateLimit";s:5:"class";s:34:"frontend\controllers\ApiController";s:4:"type";s:2:"->";}}i:5;i:6861104;}i:37;a:6:{i:0;s:56:"UPDATE `api` SET `rate_limit_remaining`=70 WHERE `id`=13";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1752514304.299757;i:4;a:2:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:43;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:71;s:8:"function";s:16:"enforceRateLimit";s:5:"class";s:34:"frontend\controllers\ApiController";s:4:"type";s:2:"->";}}i:5;i:6862440;}i:40;a:6:{i:0;s:181:"SELECT `sync_data`.`change_id`, `sync_data`.`table_name`, `sync_data`.`pk`, `sync_data`.`action`, `sync_data`.`change_time`, `sync_data`.`synced` FROM `sync_data` WHERE `synced`='0'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.300403;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:115;s:8:"function";s:3:"all";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:6870416;}i:41;a:6:{i:0;s:181:"SELECT `sync_data`.`change_id`, `sync_data`.`table_name`, `sync_data`.`pk`, `sync_data`.`action`, `sync_data`.`change_time`, `sync_data`.`synced` FROM `sync_data` WHERE `synced`='0'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.301077;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:115;s:8:"function";s:3:"all";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:6872344;}i:43;a:6:{i:0;s:62:"SELECT * FROM `notification_trigger` WHERE `route`='api/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.302646;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:6891672;}i:44;a:6:{i:0;s:62:"SELECT * FROM `notification_trigger` WHERE `route`='api/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.303439;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:6892808;}}}";s:2:"db";s:7243:"a:1:{s:8:"messages";a:14:{i:24;a:6:{i:0;s:28:"SHOW FULL COLUMNS FROM `api`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.271096;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6478128;}i:25;a:6:{i:0;s:28:"SHOW FULL COLUMNS FROM `api`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.27591;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6490760;}i:27;a:6:{i:0;s:23:"SHOW CREATE TABLE `api`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.276018;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6492952;}i:28;a:6:{i:0;s:23:"SHOW CREATE TABLE `api`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.277203;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6494888;}i:30;a:6:{i:0;s:762:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'api' AND `kcu`.`TABLE_NAME` = 'api'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.278651;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6517400;}i:31;a:6:{i:0;s:762:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'api' AND `kcu`.`TABLE_NAME` = 'api'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.28101;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6519256;}i:33;a:6:{i:0;s:142:"SELECT * FROM `api` WHERE (`token`='lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO') AND (`is_active`=1) AND (`name`='sync')";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.29152;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6785152;}i:34;a:6:{i:0;s:142:"SELECT * FROM `api` WHERE (`token`='lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO') AND (`is_active`=1) AND (`name`='sync')";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.292623;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:64;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:6787656;}i:36;a:6:{i:0;s:56:"UPDATE `api` SET `rate_limit_remaining`=70 WHERE `id`=13";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1752514304.293154;i:4;a:2:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:43;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:71;s:8:"function";s:16:"enforceRateLimit";s:5:"class";s:34:"frontend\controllers\ApiController";s:4:"type";s:2:"->";}}i:5;i:6861104;}i:37;a:6:{i:0;s:56:"UPDATE `api` SET `rate_limit_remaining`=70 WHERE `id`=13";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1752514304.299757;i:4;a:2:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:43;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:71;s:8:"function";s:16:"enforceRateLimit";s:5:"class";s:34:"frontend\controllers\ApiController";s:4:"type";s:2:"->";}}i:5;i:6862440;}i:40;a:6:{i:0;s:181:"SELECT `sync_data`.`change_id`, `sync_data`.`table_name`, `sync_data`.`pk`, `sync_data`.`action`, `sync_data`.`change_time`, `sync_data`.`synced` FROM `sync_data` WHERE `synced`='0'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.300403;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:115;s:8:"function";s:3:"all";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:6870416;}i:41;a:6:{i:0;s:181:"SELECT `sync_data`.`change_id`, `sync_data`.`table_name`, `sync_data`.`pk`, `sync_data`.`action`, `sync_data`.`change_time`, `sync_data`.`synced` FROM `sync_data` WHERE `synced`='0'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.301077;i:4;a:1:{i:0;a:5:{s:4:"file";s:58:"C:\Web\test-project\frontend\controllers\ApiController.php";s:4:"line";i:115;s:8:"function";s:3:"all";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:6872344;}i:43;a:6:{i:0;s:62:"SELECT * FROM `notification_trigger` WHERE `route`='api/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.302646;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:6891672;}i:44;a:6:{i:0;s:62:"SELECT * FROM `notification_trigger` WHERE `route`='api/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752514304.303439;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:6892808;}}}";s:5:"event";s:2814:"a:16:{i:0;a:5:{s:4:"time";d:1752514304.205457;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:1752514304.213464;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:1752514304.214485;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:34:"frontend\controllers\ApiController";}i:3;a:5:{s:4:"time";d:1752514304.23889;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:4;a:5:{s:4:"time";d:1752514304.270881;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:5;a:5:{s:4:"time";d:1752514304.292663;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"common\models\Api";}i:6;a:5:{s:4:"time";d:1752514304.292709;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"common\models\Api";}i:7;a:5:{s:4:"time";d:1752514304.293047;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"common\models\Api";}i:8;a:5:{s:4:"time";d:1752514304.300165;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"common\models\Api";}i:9;a:5:{s:4:"time";d:1752514304.301144;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:34:"frontend\controllers\ApiController";}i:10;a:5:{s:4:"time";d:1752514304.302398;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:11;a:5:{s:4:"time";d:1752514304.303471;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:12;a:5:{s:4:"time";d:1752514304.303489;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:13;a:5:{s:4:"time";d:1752514304.303496;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:14;a:5:{s:4:"time";d:1752514304.310801;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:15;a:5:{s:4:"time";d:1752514304.310905;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1752514304.164846;s:3:"end";d:1752514304.316389;s:6:"memory";i:7109560;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:1451:"a:3:{s:8:"messages";a:7:{i:10;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752514304.206618;i:4;a:0:{}i:5;i:4950456;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752514304.206636;i:4;a:0:{}i:5;i:4951048;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752514304.206643;i:4;a:0:{}i:5;i:4951640;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752514304.206649;i:4;a:0:{}i:5;i:4952232;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752514304.206655;i:4;a:0:{}i:5;i:4952824;}i:15;a:6:{i:0;s:52:"Request parsed with URL rule: api/<resource:[\w\-]+>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:1752514304.206744;i:4;a:0:{}i:5;i:4954352;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET api/<resource:[\w\-]+>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752514304.206757;i:4;a:0:{}i:5;i:4954392;}}s:5:"route";s:9:"api/index";s:6:"action";s:49:"frontend\controllers\ApiController::actionIndex()";}";s:7:"request";s:6064:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:6:{s:12:"content-type";s:0:"";s:14:"content-length";s:1:"0";s:14:"x-original-url";s:18:"/api/sync?synced=0";s:4:"host";s:14:"localhost:9000";s:13:"authorization";s:71:"Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO";s:10:"connection";s:5:"close";}s:15:"responseHeaders";a:8:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:17:"X-Ratelimit-Limit";s:3:"100";s:21:"X-Ratelimit-Remaining";s:2:"70";s:17:"X-Ratelimit-Reset";s:10:"1752516037";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68753f002da3f";s:16:"X-Debug-Duration";s:3:"147";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=68753f002da3f";}s:5:"route";s:9:"api/index";s:6:"action";s:49:"frontend\controllers\ApiController::actionIndex()";s:12:"actionParams";a:1:{s:8:"resource";s:4:"sync";}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:94:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-e94316fa-f820-4acf-8a20-eb41bff35c68";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Test Project\Test Project.config";s:11:"APP_POOL_ID";s:12:"Test Project";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:462:"C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:10:"/index.php";s:3:"URL";s:10:"/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"9000";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:10:"/index.php";s:15:"SCRIPT_FILENAME";s:42:"C:\Web\test-project\frontend\web\index.php";s:11:"REQUEST_URI";s:18:"/api/sync?synced=0";s:14:"REQUEST_METHOD";s:3:"GET";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"50251";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:8:"synced=0";s:15:"PATH_TRANSLATED";s:42:"C:\Web\test-project\frontend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/4";s:13:"INSTANCE_NAME";s:12:"TEST PROJECT";s:11:"INSTANCE_ID";s:1:"4";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\test-project\frontend\web";s:12:"CONTENT_TYPE";s:0:"";s:14:"CONTENT_LENGTH";s:1:"0";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:33:"C:\Web\test-project\frontend\web\";s:12:"APPL_MD_PATH";s:16:"/LM/W3SVC/4/ROOT";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:13:"UNENCODED_URL";s:18:"/api/sync?synced=0";s:19:"IIS_WasUrlRewritten";s:1:"1";s:19:"HTTP_X_ORIGINAL_URL";s:18:"/api/sync?synced=0";s:9:"HTTP_HOST";s:14:"localhost:9000";s:18:"HTTP_AUTHORIZATION";s:71:"Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO";s:19:"HTTP_CONTENT_LENGTH";s:1:"0";s:15:"HTTP_CONNECTION";s:5:"close";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1752514304.151736;s:12:"REQUEST_TIME";i:1752514304;}s:3:"GET";a:2:{s:8:"resource";s:4:"sync";s:6:"synced";s:1:"0";}s:4:"POST";a:0:{}s:6:"COOKIE";a:0:{}s:5:"FILES";a:0:{}s:7:"SESSION";a:0:{}}";s:4:"user";s:2:"N;";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"68753f002da3f";s:3:"url";s:39:"http://localhost:9000/api/sync?synced=0";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:3:"::1";s:4:"time";d:1752514304.151736;s:10:"statusCode";i:200;s:8:"sqlCount";i:7;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7109560;s:14:"processingTime";d:0.1513049602508545;}s:10:"exceptions";a:0:{}}