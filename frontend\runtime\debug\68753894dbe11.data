a:14:{s:6:"config";s:12881:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:57:{s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\test-project\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\test-project\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\test-project\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\test-project\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\test-project\vendor/raoul2000/yii2-workflow/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\test-project\vendor/yiisoft/yii2-jui";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\test-project\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\test-project\vendor/cornernote/yii2-workflow-manager/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\test-project\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\test-project\vendor/kartik-v/yii2-grid/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\test-project\vendor/kartik-v/yii2-popover-x/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\test-project\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\test-project\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-editable/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\test-project\vendor/yiisoft/yii2-bootstrap4/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\test-project\vendor/hail812/yii2-adminlte-widgets/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\test-project\vendor/hail812/yii2-adminlte3/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\test-project\vendor/kartik-v/yii2-widget-activeform/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\test-project\vendor/kartik-v/yii2-mpdf/src";}}s:33:"dominus77/yii2-sweetalert2-widget";a:3:{s:4:"name";s:33:"dominus77/yii2-sweetalert2-widget";s:7:"version";s:7:"2.0.0.0";s:5:"alias";a:1:{s:22:"@dominus77/sweetalert2";s:64:"C:\Web\test-project\vendor/dominus77/yii2-sweetalert2-widget/src";}}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\test-project\vendor/kartik-v/yii2-widget-select2/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\test-project\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\test-project\vendor/yiisoft/yii2-twig/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\test-project\vendor/kartik-v/yii2-helpers/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\test-project\vendor/kartik-v/yii2-builder/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\test-project\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\test-project\vendor/kartik-v/yii2-context-menu/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\test-project\vendor/kartik-v/yii2-date-range/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\test-project\vendor/kartik-v/yii2-datecontrol/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\test-project\vendor/kartik-v/yii2-detail-view/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-sortable/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-dynagrid/src";}}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-export/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\test-project\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\test-project\vendor/yiisoft/yii2-httpclient/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\test-project\vendor/kartik-v/yii2-icons/src";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\test-project\vendor/kartik-v/yii2-label-inplace/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-markdown/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\test-project\vendor/kartik-v/yii2-money/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\test-project\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\test-project\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"1.3.2.0";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\test-project\vendor/kartik-v/yii2-slider";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-social/src";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\test-project\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-tabs-x/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\test-project\vendor/kartik-v/yii2-widget-growl/src";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\test-project\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\test-project\vendor/yiisoft/yii2-debug/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\test-project\vendor/kartik-v/yii2-widget-switchinput";}}s:18:"mdmsoft/yii2-admin";a:3:{s:4:"name";s:18:"mdmsoft/yii2-admin";s:7:"version";s:8:"2.12.0.0";s:5:"alias";a:1:{s:10:"@mdm/admin";s:45:"C:\Web\test-project\vendor/mdmsoft/yii2-admin";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\test-project\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\test-project\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:25:"webzop/yii2-notifications";a:4:{s:4:"name";s:25:"webzop/yii2-notifications";s:7:"version";s:7:"0.2.0.0";s:5:"alias";a:1:{s:21:"@webzop/notifications";s:52:"C:\Web\test-project\vendor/webzop/yii2-notifications";}s:9:"bootstrap";s:30:"webzop\notifications\Bootstrap";}s:27:"tuyakhov/yii2-notifications";a:3:{s:4:"name";s:27:"tuyakhov/yii2-notifications";s:7:"version";s:7:"*******";s:5:"alias";a:2:{s:23:"@tuyakhov/notifications";s:58:"C:\Web\test-project\vendor/tuyakhov/yii2-notifications/src";s:29:"@tuyakhov/notifications/tests";s:60:"C:\Web\test-project\vendor/tuyakhov/yii2-notifications/tests";}}}}";s:3:"log";s:24544:"a:1:{s:8:"messages";a:48:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752512660.838213;i:4;a:0:{}i:5;i:2796608;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752512660.838466;i:4;a:0:{}i:5;i:2828912;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752512660.838472;i:4;a:0:{}i:5;i:2829848;}i:3;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752512660.838777;i:4;a:0:{}i:5;i:2863096;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752512660.839298;i:4;a:0:{}i:5;i:2968400;}i:5;a:6:{i:0;s:58:"Bootstrap with webzop\notifications\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752512660.839686;i:4;a:0:{}i:5;i:3036136;}i:6;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752512660.840169;i:4;a:0:{}i:5;i:3125352;}i:7;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752512660.840177;i:4;a:0:{}i:5;i:3125992;}i:8;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1752512660.844512;i:4;a:0:{}i:5;i:4120832;}i:9;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.850139;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5441888;}i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=test_project";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1752512660.850164;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5444008;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.869512;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5500880;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.870806;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5522144;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.87611;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5971984;}i:24;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752512660.898948;i:4;a:0:{}i:5;i:6681600;}i:25;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752512660.906055;i:4;a:0:{}i:5;i:6941392;}i:34;a:6:{i:0;s:35:"Route requested: 'mock-posts/index'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1752512660.90794;i:4;a:0:{}i:5;i:6984024;}i:35;a:6:{i:0;s:30:"Route to run: mock-posts/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1752512660.912641;i:4;a:0:{}i:5;i:7100040;}i:36;a:6:{i:0;s:71:"Running action: frontend\controllers\MockPostsController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1752512660.914024;i:4;a:0:{}i:5;i:7117368;}i:37;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `mock_posts`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.921621;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7284416;}i:40;a:6:{i:0;s:30:"SHOW CREATE TABLE `mock_posts`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.924994;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7299192;}i:43;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'mock_posts' AND `kcu`.`TABLE_NAME` = 'mock_posts'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.926584;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7302840;}i:46;a:6:{i:0;s:130:"GET http://localhost:9000/api/sync?synced=0
Authorization: Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO";i:1;i:4;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1752512660.936015;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:87;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:107;s:8:"function";s:13:"fetchSyncData";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7521760;}i:49;a:6:{i:0;s:41:"SELECT EXISTS(SELECT * FROM `mock_posts`)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.046613;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:110;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:7579160;}i:52;a:6:{i:0;s:122:"GET http://localhost:9000/api/posts
Authorization: Bearer F_RoBKDdxtV8DKPoqNeAqtAN2byl-95-V0LNgkz_W7VtUHSvFMJc0KfZPi8-xfVA";i:1;i:4;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:**********.04785;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:70;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:361;s:8:"function";s:12:"fetchApiData";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:116;s:8:"function";s:10:"findRecord";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7585984;}i:55;a:6:{i:0;s:174:"PUT http://localhost:9000/api/sync
Authorization: Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO
Content-Type: application/json

{"pk":29,"synced":1}";i:1;i:4;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:**********.246438;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:464;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:117;s:8:"function";s:16:"updateSyncStatus";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7592632;}i:58;a:6:{i:0;s:76:"Rendering view file: C:\Web\test-project\frontend\views\mock-posts\index.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.483119;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:7767576;}i:59;a:6:{i:0;s:24:"Loading module: gridview";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.492054;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-krajee-base\src\Config.php";s:4:"line";i:313;s:8:"function";s:9:"getModule";s:5:"class";s:15:"yii\base\Module";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1255;s:8:"function";s:9:"getModule";s:5:"class";s:18:"kartik\base\Config";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:824;s:8:"function";s:10:"initModule";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:9491712;}i:60;a:6:{i:0;s:33:"SELECT COUNT(*) FROM `mock_posts`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.500801;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10885752;}i:63;a:6:{i:0;s:35:"SELECT * FROM `mock_posts` LIMIT 20";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.504997;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\test-project\frontend\views\mock-posts\index.php";s:4:"line";i:135;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11104880;}i:66;a:6:{i:0;s:72:"Rendering view file: C:\Web\test-project\frontend\views\layouts\main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.506813;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11131872;}i:67;a:6:{i:0;s:73:"SELECT * FROM `menu_item` WHERE `parent_id` IS NULL ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.507343;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11224192;}i:70;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `menu_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.508066;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11237392;}i:73;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.509822;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11255680;}i:76;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'menu_item' AND `kcu`.`TABLE_NAME` = 'menu_item'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.51049;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11253712;}i:79;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=32 ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.512077;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11267192;}i:82;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=33 ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.512718;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11272024;}i:85;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=28 ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.513239;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11276640;}i:88;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=27 ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.513797;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11281256;}i:91;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=35 ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.514449;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11296096;}i:94;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=40 ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.515042;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11303928;}i:97;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `user_notification` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.517442;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11448488;}i:100;a:6:{i:0;s:87:"SELECT * FROM `user_notification` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.518118;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11453384;}i:103;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `user_notification`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.518634;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11465760;}i:106;a:6:{i:0;s:37:"SHOW CREATE TABLE `user_notification`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.520036;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11477776;}i:109;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notification' AND `kcu`.`TABLE_NAME` = 'user_notification'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.520661;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11480680;}i:112;a:6:{i:0;s:92:"Rendering view file: C:\Web\test-project\common\widgets\views\notificationDropdown/index.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.522206;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:27;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11489776;}i:113;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='mock-posts/index'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.526136;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11606000;}}}";s:9:"profiling";s:39997:"a:3:{s:6:"memory";i:11777128;s:4:"time";d:0.6979670524597168;s:8:"messages";a:60:{i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=test_project";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1752512660.85017;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5444816;}i:12;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=test_project";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1752512660.867463;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5488120;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.86749;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5487904;}i:14;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.869466;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5499592;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.869528;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5501792;}i:17;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.870122;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5504432;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.87083;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5523184;}i:20;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.872708;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5525712;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.876131;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5972368;}i:23;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.876741;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5974712;}i:38;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `mock_posts`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.921718;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7285704;}i:39;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `mock_posts`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.924898;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7297528;}i:41;a:6:{i:0;s:30:"SHOW CREATE TABLE `mock_posts`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.925027;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7300480;}i:42;a:6:{i:0;s:30:"SHOW CREATE TABLE `mock_posts`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.926128;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7302768;}i:44;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'mock_posts' AND `kcu`.`TABLE_NAME` = 'mock_posts'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.926607;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7304256;}i:45;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'mock_posts' AND `kcu`.`TABLE_NAME` = 'mock_posts'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.930394;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7306488;}i:47;a:6:{i:0;s:130:"GET http://localhost:9000/api/sync?synced=0
Authorization: Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO";i:1;i:80;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1752512660.936054;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:87;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:107;s:8:"function";s:13:"fetchSyncData";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7522944;}i:48;a:6:{i:0;s:130:"GET http://localhost:9000/api/sync?synced=0
Authorization: Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO";i:1;i:96;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:**********.042551;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:87;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:107;s:8:"function";s:13:"fetchSyncData";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7526824;}i:50;a:6:{i:0;s:41:"SELECT EXISTS(SELECT * FROM `mock_posts`)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.046666;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:110;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:7580072;}i:51;a:6:{i:0;s:41:"SELECT EXISTS(SELECT * FROM `mock_posts`)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.047719;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:110;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:7580968;}i:53;a:6:{i:0;s:122:"GET http://localhost:9000/api/posts
Authorization: Bearer F_RoBKDdxtV8DKPoqNeAqtAN2byl-95-V0LNgkz_W7VtUHSvFMJc0KfZPi8-xfVA";i:1;i:80;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:**********.047858;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:70;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:361;s:8:"function";s:12:"fetchApiData";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:116;s:8:"function";s:10:"findRecord";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7587544;}i:54;a:6:{i:0;s:122:"GET http://localhost:9000/api/posts
Authorization: Bearer F_RoBKDdxtV8DKPoqNeAqtAN2byl-95-V0LNgkz_W7VtUHSvFMJc0KfZPi8-xfVA";i:1;i:96;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:**********.24606;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:70;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:361;s:8:"function";s:12:"fetchApiData";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:116;s:8:"function";s:10:"findRecord";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7593176;}i:56;a:6:{i:0;s:174:"PUT http://localhost:9000/api/sync
Authorization: Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO
Content-Type: application/json

{"pk":29,"synced":1}";i:1;i:80;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:**********.246456;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:464;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:117;s:8:"function";s:16:"updateSyncStatus";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7593816;}i:57;a:6:{i:0;s:174:"PUT http://localhost:9000/api/sync
Authorization: Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO
Content-Type: application/json

{"pk":29,"synced":1}";i:1;i:96;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:**********.4812;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:464;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:117;s:8:"function";s:16:"updateSyncStatus";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7597664;}i:61;a:6:{i:0;s:33:"SELECT COUNT(*) FROM `mock_posts`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.500822;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10887416;}i:62;a:6:{i:0;s:33:"SELECT COUNT(*) FROM `mock_posts`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.502646;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10889040;}i:64;a:6:{i:0;s:35:"SELECT * FROM `mock_posts` LIMIT 20";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.505014;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\test-project\frontend\views\mock-posts\index.php";s:4:"line";i:135;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11106544;}i:65;a:6:{i:0;s:35:"SELECT * FROM `mock_posts` LIMIT 20";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.505544;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\test-project\frontend\views\mock-posts\index.php";s:4:"line";i:135;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11110984;}i:68;a:6:{i:0;s:73:"SELECT * FROM `menu_item` WHERE `parent_id` IS NULL ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.507355;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11225480;}i:69;a:6:{i:0;s:73:"SELECT * FROM `menu_item` WHERE `parent_id` IS NULL ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.508022;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11232832;}i:71;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `menu_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.508076;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11238680;}i:72;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `menu_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.509773;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11254016;}i:74;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.509842;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11256968;}i:75;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.510241;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11259640;}i:77;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'menu_item' AND `kcu`.`TABLE_NAME` = 'menu_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.510509;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11255128;}i:78;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'menu_item' AND `kcu`.`TABLE_NAME` = 'menu_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.511881;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11258056;}i:80;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=32 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.512089;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11268696;}i:81;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=32 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.512646;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11270432;}i:83;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=33 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.512728;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11273528;}i:84;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=33 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.513177;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11275264;}i:86;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=28 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.513249;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11278144;}i:87;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=28 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.513734;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11279880;}i:89;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=27 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.513806;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11282760;}i:90;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=27 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.514333;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11288392;}i:92;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=35 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.514464;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11297600;}i:93;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=35 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.514935;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11302328;}i:95;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=40 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.515052;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11305432;}i:96;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=40 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.515536;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11310144;}i:98;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `user_notification` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.517472;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11450264;}i:99;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `user_notification` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.518042;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11451960;}i:101;a:6:{i:0;s:87:"SELECT * FROM `user_notification` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.518128;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11455264;}i:102;a:6:{i:0;s:87:"SELECT * FROM `user_notification` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.518587;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11462088;}i:104;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `user_notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.518645;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11467424;}i:105;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `user_notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.520009;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11475728;}i:107;a:6:{i:0;s:37:"SHOW CREATE TABLE `user_notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.520046;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11479440;}i:108;a:6:{i:0;s:37:"SHOW CREATE TABLE `user_notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.520579;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11482368;}i:110;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notification' AND `kcu`.`TABLE_NAME` = 'user_notification'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.520671;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11482472;}i:111;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notification' AND `kcu`.`TABLE_NAME` = 'user_notification'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.521954;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11486320;}i:114;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='mock-posts/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.526151;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11607128;}i:115;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='mock-posts/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.52703;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11608280;}}}";s:2:"db";s:34615:"a:1:{s:8:"messages";a:52:{i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.86749;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5487904;}i:14;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.869466;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5499592;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.869528;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5501792;}i:17;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.870122;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5504432;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.87083;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5523184;}i:20;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.872708;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5525712;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.876131;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5972368;}i:23;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.876741;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5974712;}i:38;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `mock_posts`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.921718;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7285704;}i:39;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `mock_posts`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.924898;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7297528;}i:41;a:6:{i:0;s:30:"SHOW CREATE TABLE `mock_posts`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.925027;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7300480;}i:42;a:6:{i:0;s:30:"SHOW CREATE TABLE `mock_posts`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.926128;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7302768;}i:44;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'mock_posts' AND `kcu`.`TABLE_NAME` = 'mock_posts'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.926607;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7304256;}i:45;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'mock_posts' AND `kcu`.`TABLE_NAME` = 'mock_posts'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752512660.930394;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7306488;}i:50;a:6:{i:0;s:41:"SELECT EXISTS(SELECT * FROM `mock_posts`)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.046666;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:110;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:7580072;}i:51;a:6:{i:0;s:41:"SELECT EXISTS(SELECT * FROM `mock_posts`)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.047719;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:110;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:7580968;}i:61;a:6:{i:0;s:33:"SELECT COUNT(*) FROM `mock_posts`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.500822;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10887416;}i:62;a:6:{i:0;s:33:"SELECT COUNT(*) FROM `mock_posts`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.502646;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10889040;}i:64;a:6:{i:0;s:35:"SELECT * FROM `mock_posts` LIMIT 20";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.505014;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\test-project\frontend\views\mock-posts\index.php";s:4:"line";i:135;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11106544;}i:65;a:6:{i:0;s:35:"SELECT * FROM `mock_posts` LIMIT 20";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.505544;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\test-project\frontend\views\mock-posts\index.php";s:4:"line";i:135;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11110984;}i:68;a:6:{i:0;s:73:"SELECT * FROM `menu_item` WHERE `parent_id` IS NULL ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.507355;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11225480;}i:69;a:6:{i:0;s:73:"SELECT * FROM `menu_item` WHERE `parent_id` IS NULL ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.508022;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11232832;}i:71;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `menu_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.508076;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11238680;}i:72;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `menu_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.509773;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11254016;}i:74;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.509842;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11256968;}i:75;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.510241;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11259640;}i:77;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'menu_item' AND `kcu`.`TABLE_NAME` = 'menu_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.510509;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11255128;}i:78;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'menu_item' AND `kcu`.`TABLE_NAME` = 'menu_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.511881;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11258056;}i:80;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=32 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.512089;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11268696;}i:81;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=32 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.512646;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11270432;}i:83;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=33 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.512728;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11273528;}i:84;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=33 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.513177;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11275264;}i:86;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=28 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.513249;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11278144;}i:87;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=28 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.513734;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11279880;}i:89;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=27 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.513806;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11282760;}i:90;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=27 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.514333;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11288392;}i:92;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=35 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.514464;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11297600;}i:93;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=35 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.514935;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11302328;}i:95;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=40 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.515052;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11305432;}i:96;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=40 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.515536;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11310144;}i:98;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `user_notification` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.517472;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11450264;}i:99;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `user_notification` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.518042;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11451960;}i:101;a:6:{i:0;s:87:"SELECT * FROM `user_notification` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.518128;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11455264;}i:102;a:6:{i:0;s:87:"SELECT * FROM `user_notification` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.518587;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11462088;}i:104;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `user_notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.518645;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11467424;}i:105;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `user_notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.520009;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11475728;}i:107;a:6:{i:0;s:37:"SHOW CREATE TABLE `user_notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.520046;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11479440;}i:108;a:6:{i:0;s:37:"SHOW CREATE TABLE `user_notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.520579;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11482368;}i:110;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notification' AND `kcu`.`TABLE_NAME` = 'user_notification'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.520671;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11482472;}i:111;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notification' AND `kcu`.`TABLE_NAME` = 'user_notification'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.521954;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:130;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11486320;}i:114;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='mock-posts/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.526151;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11607128;}i:115;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='mock-posts/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.52703;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11608280;}}}";s:5:"event";s:42191:"a:229:{i:0;a:5:{s:4:"time";d:1752512660.848549;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1752512660.867453;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1752512660.878749;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:1752512660.878944;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:1752512660.891921;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:1752512660.906827;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1752512660.913079;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:7;a:5:{s:4:"time";d:1752512660.913962;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"frontend\controllers\MockPostsController";}i:8;a:5:{s:4:"time";d:1752512660.915645;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"common\models\search\MockPostsSearch";}i:9;a:5:{s:4:"time";d:1752512660.915715;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:10;a:5:{s:4:"time";d:1752512660.917993;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"common\models\search\MockPostsSearch";}i:11;a:5:{s:4:"time";d:1752512660.930538;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"common\models\search\MockPostsSearch";}i:12;a:5:{s:4:"time";d:1752512660.934774;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:13;a:5:{s:4:"time";d:1752512660.934789;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:14;a:5:{s:4:"time";d:**********.0436;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:15;a:5:{s:4:"time";d:**********.043616;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:16;a:5:{s:4:"time";d:**********.046466;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:17;a:5:{s:4:"time";d:**********.047794;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:18;a:5:{s:4:"time";d:**********.047804;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:19;a:5:{s:4:"time";d:**********.246162;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:20;a:5:{s:4:"time";d:**********.246179;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:21;a:5:{s:4:"time";d:**********.246373;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:22;a:5:{s:4:"time";d:**********.24638;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:23;a:5:{s:4:"time";d:**********.481249;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:24;a:5:{s:4:"time";d:**********.481255;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:25;a:5:{s:4:"time";d:**********.483111;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:26;a:5:{s:4:"time";d:**********.491496;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:27;a:5:{s:4:"time";d:**********.491555;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:28;a:5:{s:4:"time";d:**********.491563;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:29;a:5:{s:4:"time";d:**********.492644;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:30;a:5:{s:4:"time";d:**********.492655;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:31;a:5:{s:4:"time";d:**********.492661;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:32;a:5:{s:4:"time";d:**********.492681;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:33;a:5:{s:4:"time";d:**********.495691;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:34;a:5:{s:4:"time";d:**********.495713;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:35;a:5:{s:4:"time";d:**********.495724;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:36;a:5:{s:4:"time";d:**********.495728;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:37;a:5:{s:4:"time";d:**********.495732;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:38;a:5:{s:4:"time";d:**********.495735;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:39;a:5:{s:4:"time";d:**********.495744;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:40;a:5:{s:4:"time";d:**********.495748;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:41;a:5:{s:4:"time";d:**********.495753;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:42;a:5:{s:4:"time";d:**********.495757;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:43;a:5:{s:4:"time";d:**********.49576;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:44;a:5:{s:4:"time";d:**********.495763;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:45;a:5:{s:4:"time";d:**********.495767;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:46;a:5:{s:4:"time";d:**********.49577;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:47;a:5:{s:4:"time";d:**********.495774;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:48;a:5:{s:4:"time";d:**********.495777;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:49;a:5:{s:4:"time";d:**********.49578;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:50;a:5:{s:4:"time";d:**********.495783;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:51;a:5:{s:4:"time";d:**********.495791;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:52;a:5:{s:4:"time";d:**********.495795;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:53;a:5:{s:4:"time";d:**********.495798;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:54;a:5:{s:4:"time";d:**********.495801;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:55;a:5:{s:4:"time";d:**********.495804;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:56;a:5:{s:4:"time";d:**********.495808;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:57;a:5:{s:4:"time";d:**********.495812;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:58;a:5:{s:4:"time";d:**********.495815;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:59;a:5:{s:4:"time";d:**********.495818;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:60;a:5:{s:4:"time";d:**********.495822;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:61;a:5:{s:4:"time";d:**********.495825;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:62;a:5:{s:4:"time";d:**********.495827;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:63;a:5:{s:4:"time";d:**********.495831;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:64;a:5:{s:4:"time";d:**********.495834;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:65;a:5:{s:4:"time";d:**********.495837;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:66;a:5:{s:4:"time";d:**********.49584;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:67;a:5:{s:4:"time";d:**********.495844;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:68;a:5:{s:4:"time";d:**********.495857;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:69;a:5:{s:4:"time";d:**********.49586;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:70;a:5:{s:4:"time";d:**********.495864;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:71;a:5:{s:4:"time";d:**********.495867;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:72;a:5:{s:4:"time";d:**********.49587;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:73;a:5:{s:4:"time";d:**********.495873;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:74;a:5:{s:4:"time";d:**********.496911;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:75;a:5:{s:4:"time";d:**********.496926;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:76;a:5:{s:4:"time";d:**********.497117;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:77;a:5:{s:4:"time";d:**********.497126;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:78;a:5:{s:4:"time";d:**********.49713;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:79;a:5:{s:4:"time";d:**********.497136;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:80;a:5:{s:4:"time";d:**********.497512;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:81;a:5:{s:4:"time";d:**********.49843;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:82;a:5:{s:4:"time";d:**********.498773;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:83;a:5:{s:4:"time";d:**********.499514;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:84;a:5:{s:4:"time";d:**********.49952;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:85;a:5:{s:4:"time";d:**********.499524;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:86;a:5:{s:4:"time";d:**********.499543;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:87;a:5:{s:4:"time";d:**********.499758;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:88;a:5:{s:4:"time";d:**********.499763;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:89;a:5:{s:4:"time";d:**********.499923;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:90;a:5:{s:4:"time";d:**********.499927;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:91;a:5:{s:4:"time";d:**********.499931;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:92;a:5:{s:4:"time";d:**********.499977;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:93;a:5:{s:4:"time";d:**********.500708;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:94;a:5:{s:4:"time";d:**********.500734;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:95;a:5:{s:4:"time";d:**********.500741;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:96;a:5:{s:4:"time";d:**********.500744;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:97;a:5:{s:4:"time";d:**********.500752;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:98;a:5:{s:4:"time";d:**********.503335;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"kartik\bs5dropdown\ButtonDropdown";}i:99;a:5:{s:4:"time";d:**********.503343;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"kartik\bs5dropdown\ButtonDropdown";}i:100;a:5:{s:4:"time";d:**********.503815;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\Button";}i:101;a:5:{s:4:"time";d:**********.503824;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\Button";}i:102;a:5:{s:4:"time";d:**********.503847;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\Button";}i:103;a:5:{s:4:"time";d:**********.504222;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:104;a:5:{s:4:"time";d:**********.504232;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:105;a:5:{s:4:"time";d:**********.504623;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:106;a:5:{s:4:"time";d:**********.504635;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"kartik\bs5dropdown\ButtonDropdown";}i:107;a:5:{s:4:"time";d:**********.504716;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:108;a:5:{s:4:"time";d:**********.504724;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:109;a:5:{s:4:"time";d:**********.504766;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:110;a:5:{s:4:"time";d:**********.504892;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:111;a:5:{s:4:"time";d:**********.504946;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"common\models\MockPosts";}i:112;a:5:{s:4:"time";d:**********.505566;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"common\models\MockPosts";}i:113;a:5:{s:4:"time";d:**********.50559;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"common\models\MockPosts";}i:114;a:5:{s:4:"time";d:**********.506052;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:115;a:5:{s:4:"time";d:**********.506096;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:116;a:5:{s:4:"time";d:**********.506099;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:117;a:5:{s:4:"time";d:**********.506103;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:118;a:5:{s:4:"time";d:**********.506118;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:119;a:5:{s:4:"time";d:**********.506141;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:120;a:5:{s:4:"time";d:**********.506144;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:121;a:5:{s:4:"time";d:**********.506484;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:122;a:5:{s:4:"time";d:**********.506704;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:123;a:5:{s:4:"time";d:**********.506726;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:124;a:5:{s:4:"time";d:**********.506746;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:125;a:5:{s:4:"time";d:**********.506811;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:126;a:5:{s:4:"time";d:**********.507294;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:127;a:5:{s:4:"time";d:**********.508041;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:128;a:5:{s:4:"time";d:**********.511946;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:129;a:5:{s:4:"time";d:**********.511962;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:130;a:5:{s:4:"time";d:**********.511971;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:131;a:5:{s:4:"time";d:**********.511978;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:132;a:5:{s:4:"time";d:**********.511985;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:133;a:5:{s:4:"time";d:**********.511994;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:134;a:5:{s:4:"time";d:**********.511996;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:135;a:5:{s:4:"time";d:**********.511999;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:136;a:5:{s:4:"time";d:**********.512001;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:137;a:5:{s:4:"time";d:**********.512003;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:138;a:5:{s:4:"time";d:**********.512005;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:139;a:5:{s:4:"time";d:**********.51202;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:140;a:5:{s:4:"time";d:**********.512673;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:141;a:5:{s:4:"time";d:**********.513201;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:142;a:5:{s:4:"time";d:**********.513758;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:143;a:5:{s:4:"time";d:**********.514352;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:144;a:5:{s:4:"time";d:**********.514371;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:145;a:5:{s:4:"time";d:**********.514381;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:146;a:5:{s:4:"time";d:**********.514388;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:147;a:5:{s:4:"time";d:**********.514396;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:148;a:5:{s:4:"time";d:**********.514398;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:149;a:5:{s:4:"time";d:**********.5144;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:150;a:5:{s:4:"time";d:**********.514402;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:151;a:5:{s:4:"time";d:**********.514414;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:152;a:5:{s:4:"time";d:**********.514954;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:153;a:5:{s:4:"time";d:**********.514972;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:154;a:5:{s:4:"time";d:**********.514981;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:155;a:5:{s:4:"time";d:**********.514989;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:156;a:5:{s:4:"time";d:**********.514991;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:157;a:5:{s:4:"time";d:**********.514993;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:158;a:5:{s:4:"time";d:**********.515005;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:159;a:5:{s:4:"time";d:**********.515556;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:160;a:5:{s:4:"time";d:**********.515575;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:161;a:5:{s:4:"time";d:**********.515584;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:162;a:5:{s:4:"time";d:**********.515592;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:163;a:5:{s:4:"time";d:**********.515594;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:164;a:5:{s:4:"time";d:**********.515596;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:165;a:5:{s:4:"time";d:**********.515668;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:166;a:5:{s:4:"time";d:**********.515735;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:167;a:5:{s:4:"time";d:**********.516133;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\NavBar";}i:168;a:5:{s:4:"time";d:**********.516532;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\bootstrap5\Nav";}i:169;a:5:{s:4:"time";d:**********.516549;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\bootstrap5\Nav";}i:170;a:5:{s:4:"time";d:**********.516635;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:171;a:5:{s:4:"time";d:**********.51664;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:172;a:5:{s:4:"time";d:**********.516688;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:173;a:5:{s:4:"time";d:**********.516713;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:174;a:5:{s:4:"time";d:**********.516717;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:175;a:5:{s:4:"time";d:**********.516755;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:176;a:5:{s:4:"time";d:**********.516777;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:177;a:5:{s:4:"time";d:**********.51678;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:178;a:5:{s:4:"time";d:**********.516812;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:179;a:5:{s:4:"time";d:**********.516824;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\bootstrap5\Nav";}i:180;a:5:{s:4:"time";d:**********.516993;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"common\widgets\NotificationDropdown";}i:181;a:5:{s:4:"time";d:**********.516998;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"common\widgets\NotificationDropdown";}i:182;a:5:{s:4:"time";d:**********.517382;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:183;a:5:{s:4:"time";d:**********.518069;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:184;a:5:{s:4:"time";d:**********.518608;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:185;a:5:{s:4:"time";d:**********.522022;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:186;a:5:{s:4:"time";d:**********.522032;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:187;a:5:{s:4:"time";d:**********.522039;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:188;a:5:{s:4:"time";d:**********.522044;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:189;a:5:{s:4:"time";d:**********.522049;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:190;a:5:{s:4:"time";d:**********.522054;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:191;a:5:{s:4:"time";d:**********.52206;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:192;a:5:{s:4:"time";d:**********.522065;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:193;a:5:{s:4:"time";d:**********.522071;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:194;a:5:{s:4:"time";d:**********.522074;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:195;a:5:{s:4:"time";d:**********.522076;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:196;a:5:{s:4:"time";d:**********.522078;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:197;a:5:{s:4:"time";d:**********.522079;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:198;a:5:{s:4:"time";d:**********.522081;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:199;a:5:{s:4:"time";d:**********.522083;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:200;a:5:{s:4:"time";d:**********.522085;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:201;a:5:{s:4:"time";d:**********.522087;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:202;a:5:{s:4:"time";d:**********.522203;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:203;a:5:{s:4:"time";d:**********.522468;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:204;a:5:{s:4:"time";d:**********.522475;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"common\widgets\NotificationDropdown";}i:205;a:5:{s:4:"time";d:**********.522503;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:206;a:5:{s:4:"time";d:**********.522514;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:207;a:5:{s:4:"time";d:**********.522557;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:208;a:5:{s:4:"time";d:**********.522563;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\NavBar";}i:209;a:5:{s:4:"time";d:**********.522573;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\NavBar";}i:210;a:5:{s:4:"time";d:**********.522904;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"dominus77\sweetalert2\Alert";}i:211;a:5:{s:4:"time";d:**********.522908;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"dominus77\sweetalert2\Alert";}i:212;a:5:{s:4:"time";d:**********.522919;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"dominus77\sweetalert2\Alert";}i:213;a:5:{s:4:"time";d:**********.523245;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:214;a:5:{s:4:"time";d:**********.523252;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:215;a:5:{s:4:"time";d:**********.523291;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:216;a:5:{s:4:"time";d:**********.523474;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"common\widgets\Alert";}i:217;a:5:{s:4:"time";d:**********.523484;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"common\widgets\Alert";}i:218;a:5:{s:4:"time";d:**********.523491;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"common\widgets\Alert";}i:219;a:5:{s:4:"time";d:**********.524246;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:220;a:5:{s:4:"time";d:**********.524398;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:221;a:5:{s:4:"time";d:**********.525894;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:222;a:5:{s:4:"time";d:**********.525903;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"frontend\controllers\MockPostsController";}i:223;a:5:{s:4:"time";d:**********.526074;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:224;a:5:{s:4:"time";d:**********.527073;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:225;a:5:{s:4:"time";d:**********.527092;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:226;a:5:{s:4:"time";d:**********.527116;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:227;a:5:{s:4:"time";d:**********.527674;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:228;a:5:{s:4:"time";d:**********.528013;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1752512660.83296;s:3:"end";d:**********.531839;s:6:"memory";i:11777128;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:1667:"a:3:{s:8:"messages";a:8:{i:26;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752512660.907781;i:4;a:0:{}i:5;i:6979600;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752512660.907812;i:4;a:0:{}i:5;i:6980192;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752512660.907831;i:4;a:0:{}i:5;i:6980784;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752512660.907846;i:4;a:0:{}i:5;i:6981376;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752512660.90786;i:4;a:0:{}i:5;i:6981968;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET api/<resource:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752512660.907891;i:4;a:0:{}i:5;i:6982616;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:26:"PUT api/<resource:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752512660.907909;i:4;a:0:{}i:5;i:6983264;}i:33;a:6:{i:0;s:55:"No matching URL rules. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752512660.907919;i:4;a:0:{}i:5;i:6984120;}}s:5:"route";s:16:"mock-posts/index";s:6:"action";s:55:"frontend\controllers\MockPostsController::actionIndex()";}";s:7:"request";s:11202:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:21:{s:12:"content-type";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:14:"content-length";s:1:"0";s:14:"x-original-url";s:42:"/mock-posts/index?_pjax=%23model-datatable";s:14:"sec-fetch-dest";s:5:"empty";s:14:"sec-fetch-mode";s:4:"cors";s:14:"sec-fetch-site";s:11:"same-origin";s:16:"x-requested-with";s:14:"XMLHttpRequest";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:64:""Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:12:"x-csrf-token";s:88:"FWRNGctJlA3K6HDAlSSQBISuRPgVAnJzSLScmHEjmgZ4Ix1fmhbeeryHFLThdalbsOkUlXA3Il4t3_PVAHf9Tg==";s:6:"x-pjax";s:4:"true";s:16:"x-pjax-container";s:16:"#model-datatable";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:7:"referer";s:38:"http://localhost:9000/mock-posts/index";s:4:"host";s:14:"localhost:9000";s:6:"cookie";s:954:"_identity-frontend=1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; advanced-frontend=lj75dob3daup08nk4a8tootgbt; _csrf-frontend=987794102ea9939de322b56534be5c83dc38b85b3c5875dcd6a75e6555987485a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22mGPFQ_JwvodttQ9_4GPme5P-ekoMqTgH%22%3B%7D; advanced-backend-test=0e3n9oj5c286gmtljdrc2j5hu5; _csrf-backend=662260930e32dfab483a7cb4ebeea2fc94ee5f0fd8822614b5c1202ccdd85223a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22uC8kUI6_jtEX6ckmjU3H4rhhguwLkXAD%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:22:"text/html, */*; q=0.01";s:10:"connection";s:10:"keep-alive";}s:15:"responseHeaders";a:9:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68753894dbe11";s:16:"X-Debug-Duration";s:3:"696";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=68753894dbe11";s:10:"Set-Cookie";s:313:"_identity-frontend=1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; expires=Wed, 13 Aug 2025 17:04:20 GMT; Max-Age=2591999; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:16:"mock-posts/index";s:6:"action";s:55:"frontend\controllers\MockPostsController::actionIndex()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:1;s:6:"isPjax";b:1;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:110:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-8aa90166-b27a-43f7-bb66-25b14d0f1476";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Test Project\Test Project.config";s:11:"APP_POOL_ID";s:12:"Test Project";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:462:"C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:10:"/index.php";s:3:"URL";s:10:"/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"9000";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:10:"/index.php";s:15:"SCRIPT_FILENAME";s:42:"C:\Web\test-project\frontend\web\index.php";s:11:"REQUEST_URI";s:42:"/mock-posts/index?_pjax=%23model-datatable";s:14:"REQUEST_METHOD";s:3:"GET";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"65316";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:24:"_pjax=%23model-datatable";s:15:"PATH_TRANSLATED";s:42:"C:\Web\test-project\frontend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/4";s:13:"INSTANCE_NAME";s:12:"TEST PROJECT";s:11:"INSTANCE_ID";s:1:"4";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\test-project\frontend\web";s:12:"CONTENT_TYPE";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:14:"CONTENT_LENGTH";s:1:"0";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:33:"C:\Web\test-project\frontend\web\";s:12:"APPL_MD_PATH";s:16:"/LM/W3SVC/4/ROOT";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:13:"UNENCODED_URL";s:42:"/mock-posts/index?_pjax=%23model-datatable";s:19:"IIS_WasUrlRewritten";s:1:"1";s:19:"HTTP_X_ORIGINAL_URL";s:42:"/mock-posts/index?_pjax=%23model-datatable";s:19:"HTTP_SEC_FETCH_DEST";s:5:"empty";s:19:"HTTP_SEC_FETCH_MODE";s:4:"cors";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:21:"HTTP_X_REQUESTED_WITH";s:14:"XMLHttpRequest";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:64:""Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:17:"HTTP_X_CSRF_TOKEN";s:88:"FWRNGctJlA3K6HDAlSSQBISuRPgVAnJzSLScmHEjmgZ4Ix1fmhbeeryHFLThdalbsOkUlXA3Il4t3_PVAHf9Tg==";s:11:"HTTP_X_PJAX";s:4:"true";s:21:"HTTP_X_PJAX_CONTAINER";s:16:"#model-datatable";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:12:"HTTP_REFERER";s:38:"http://localhost:9000/mock-posts/index";s:9:"HTTP_HOST";s:14:"localhost:9000";s:11:"HTTP_COOKIE";s:954:"_identity-frontend=1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; advanced-frontend=lj75dob3daup08nk4a8tootgbt; _csrf-frontend=987794102ea9939de322b56534be5c83dc38b85b3c5875dcd6a75e6555987485a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22mGPFQ_JwvodttQ9_4GPme5P-ekoMqTgH%22%3B%7D; advanced-backend-test=0e3n9oj5c286gmtljdrc2j5hu5; _csrf-backend=662260930e32dfab483a7cb4ebeea2fc94ee5f0fd8822614b5c1202ccdd85223a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22uC8kUI6_jtEX6ckmjU3H4rhhguwLkXAD%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:22:"text/html, */*; q=0.01";s:17:"HTTP_CONTENT_TYPE";s:48:"application/x-www-form-urlencoded; charset=UTF-8";s:19:"HTTP_CONTENT_LENGTH";s:1:"0";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1752512660.824504;s:12:"REQUEST_TIME";i:1752512660;}s:3:"GET";a:1:{s:5:"_pjax";s:16:"#model-datatable";}s:4:"POST";a:0:{}s:6:"COOKIE";a:7:{s:18:"_identity-frontend";s:158:"1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[1,"RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo",2592000]";}";s:16:"sidebar-collapse";s:5:"false";s:17:"_identity-backend";s:157:"946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo",2592000]";}";s:17:"advanced-frontend";s:26:"lj75dob3daup08nk4a8tootgbt";s:14:"_csrf-frontend";s:140:"987794102ea9939de322b56534be5c83dc38b85b3c5875dcd6a75e6555987485a:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"mGPFQ_JwvodttQ9_4GPme5P-ekoMqTgH";}";s:21:"advanced-backend-test";s:26:"0e3n9oj5c286gmtljdrc2j5hu5";s:13:"_csrf-backend";s:139:"662260930e32dfab483a7cb4ebeea2fc94ee5f0fd8822614b5c1202ccdd85223a:2:{i:0;s:13:"_csrf-backend";i:1;s:32:"uC8kUI6_jtEX6ckmjU3H4rhhguwLkXAD";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";s:32:"RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo";}}";s:4:"user";s:2311:"a:5:{s:2:"id";i:1;s:8:"identity";a:11:{s:2:"id";s:1:"1";s:8:"username";s:8:"'qwerty'";s:8:"auth_key";s:34:"'RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo'";s:13:"password_hash";s:62:"'$2y$13$PvAUhC2Ak1Dg55cbPHYsDu8KTgzGOeu08y2SUHvEoYbw7Xqvr8wMG'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1746551344";s:10:"updated_at";s:10:"1746551344";s:18:"verification_token";s:45:"'HyyhnMnjystP5EIGPwdessQ_HqW8DtaX_1746551344'";s:7:"role_id";s:1:"1";}s:10:"attributes";a:11:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:13:"Password Hash";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:7:"Role Id";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-2";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-3";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:9602:"a:17:{s:31:"yii2ajaxcrud\ajaxcrud\CrudAsset";a:9:{s:10:"sourcePath";s:64:"C:\Web\test-project\vendor\biladina\yii2-ajaxcrud-bs4\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\827c4fee";s:7:"baseUrl";s:16:"/assets/827c4fee";s:7:"depends";a:5:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:35:"yii\bootstrap5\BootstrapPluginAsset";i:3;s:25:"kartik\grid\GridViewAsset";i:4;s:38:"yii2ajaxcrud\ajaxcrud\FontAwesomeAsset";}s:2:"js";a:2:{i:0;s:14:"ModalRemote.js";i:1;s:11:"ajaxcrud.js";}s:3:"css";a:1:{i:0;s:12:"ajaxcrud.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\test-project\vendor\yiisoft\yii2/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\215e7eb0";s:7:"baseUrl";s:16:"/assets/215e7eb0";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:6:"yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:50:"C:\Web\test-project/vendor/bower-asset/jquery/dist";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\d95451c1";s:7:"baseUrl";s:16:"/assets/d95451c1";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap5\BootstrapAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\test-project/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\f9baa076";s:7:"baseUrl";s:16:"/assets/f9baa076";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:1:{s:8:"position";i:1;}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap5\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\test-project/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\f9baa076";s:7:"baseUrl";s:16:"/assets/f9baa076";s:7:"depends";a:1:{i:0;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:25:"kartik\grid\GridViewAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\8db62d20";s:7:"baseUrl";s:16:"/assets/8db62d20";s:7:"depends";a:4:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:22:"yii\grid\GridViewAsset";i:2;s:16:"yii\web\YiiAsset";i:3;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:15:"css/kv-grid.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:25:"kartik\dialog\DialogAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\test-project\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\eb409b32";s:7:"baseUrl";s:16:"/assets/eb409b32";s:7:"depends";a:1:{i:0;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:12:"js/dialog.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:1:{s:8:"position";i:1;}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:22:"yii\grid\GridViewAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\test-project\vendor\yiisoft\yii2/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\215e7eb0";s:7:"baseUrl";s:16:"/assets/215e7eb0";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:15:"yii.gridView.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:38:"yii2ajaxcrud\ajaxcrud\FontAwesomeAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\test-project/vendor/fortawesome/font-awesome";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\d7c10c94";s:7:"baseUrl";s:16:"/assets/d7c10c94";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:13:"js/all.min.js";}s:3:"css";a:1:{i:0;s:15:"css/all.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:31:"kartik\grid\CheckboxColumnAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\8db62d20";s:7:"baseUrl";s:16:"/assets/8db62d20";s:7:"depends";a:3:{i:0;s:25:"kartik\grid\GridViewAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/kv-grid-checkbox.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:34:"kartik\dialog\DialogBootstrapAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\test-project\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\eb409b32";s:7:"baseUrl";s:16:"/assets/eb409b32";s:7:"depends";a:4:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";i:3;s:35:"yii\bootstrap5\BootstrapPluginAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap-dialog.js";}s:3:"css";a:1:{i:0;s:28:"css/bootstrap-dialog-bs4.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:1;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:28:"kartik\dialog\DialogYiiAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\test-project\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\eb409b32";s:7:"baseUrl";s:16:"/assets/eb409b32";s:7:"depends";a:3:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:16:"js/dialog-yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:27:"kartik\grid\GridExportAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\8db62d20";s:7:"baseUrl";s:16:"/assets/8db62d20";s:7:"depends";a:3:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:20:"js/kv-grid-export.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:34:"kartik\grid\GridResizeColumnsAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\8db62d20";s:7:"baseUrl";s:16:"/assets/8db62d20";s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:25:"kartik\grid\GridViewAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:29:"js/jquery.resizableColumns.js";}s:3:"css";a:1:{i:0;s:31:"css/jquery.resizableColumns.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:21:"yii\widgets\PjaxAsset";a:9:{s:10:"sourcePath";s:48:"C:\Web\test-project/vendor/bower-asset/yii2-pjax";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\71798dde";s:7:"baseUrl";s:16:"/assets/71798dde";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:14:"jquery.pjax.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:32:"kartik\bs5dropdown\DropdownAsset";a:17:{s:10:"sourcePath";s:71:"C:\Web\test-project\vendor\kartik-v\yii2-bootstrap5-dropdown\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\74d8da42";s:7:"baseUrl";s:16:"/assets/74d8da42";s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:35:"yii\bootstrap5\BootstrapPluginAsset";}s:2:"js";a:1:{i:0;s:14:"js/dropdown.js";}s:3:"css";a:1:{i:0;s:16:"css/dropdown.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:1;s:9:"bsVersion";s:3:"5.x";s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:24:"frontend\assets\AppAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:32:"C:\Web\test-project\frontend\web";s:7:"baseUrl";s:0:"";s:7:"depends";a:2:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:10:"js/main.js";}s:3:"css";a:1:{i:0;s:12:"css/site.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}}";s:7:"summary";a:13:{s:3:"tag";s:13:"68753894dbe11";s:3:"url";s:63:"http://localhost:9000/mock-posts/index?_pjax=%23model-datatable";s:4:"ajax";i:1;s:6:"method";s:3:"GET";s:2:"ip";s:3:"::1";s:4:"time";d:1752512660.824504;s:10:"statusCode";i:200;s:8:"sqlCount";i:26;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11777128;s:14:"processingTime";d:0.6979670524597168;}s:10:"exceptions";a:0:{}}