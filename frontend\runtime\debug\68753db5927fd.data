a:14:{s:6:"config";s:12881:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:57:{s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\test-project\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\test-project\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\test-project\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\test-project\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\test-project\vendor/raoul2000/yii2-workflow/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\test-project\vendor/yiisoft/yii2-jui";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\test-project\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\test-project\vendor/cornernote/yii2-workflow-manager/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\test-project\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\test-project\vendor/kartik-v/yii2-grid/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\test-project\vendor/kartik-v/yii2-popover-x/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\test-project\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\test-project\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-editable/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\test-project\vendor/yiisoft/yii2-bootstrap4/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\test-project\vendor/hail812/yii2-adminlte-widgets/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\test-project\vendor/hail812/yii2-adminlte3/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\test-project\vendor/kartik-v/yii2-widget-activeform/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\test-project\vendor/kartik-v/yii2-mpdf/src";}}s:33:"dominus77/yii2-sweetalert2-widget";a:3:{s:4:"name";s:33:"dominus77/yii2-sweetalert2-widget";s:7:"version";s:7:"2.0.0.0";s:5:"alias";a:1:{s:22:"@dominus77/sweetalert2";s:64:"C:\Web\test-project\vendor/dominus77/yii2-sweetalert2-widget/src";}}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\test-project\vendor/kartik-v/yii2-widget-select2/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\test-project\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\test-project\vendor/yiisoft/yii2-twig/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\test-project\vendor/kartik-v/yii2-helpers/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\test-project\vendor/kartik-v/yii2-builder/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\test-project\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\test-project\vendor/kartik-v/yii2-context-menu/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\test-project\vendor/kartik-v/yii2-date-range/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\test-project\vendor/kartik-v/yii2-datecontrol/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\test-project\vendor/kartik-v/yii2-detail-view/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-sortable/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-dynagrid/src";}}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-export/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\test-project\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\test-project\vendor/yiisoft/yii2-httpclient/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\test-project\vendor/kartik-v/yii2-icons/src";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\test-project\vendor/kartik-v/yii2-label-inplace/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-markdown/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\test-project\vendor/kartik-v/yii2-money/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\test-project\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\test-project\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"1.3.2.0";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\test-project\vendor/kartik-v/yii2-slider";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-social/src";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\test-project\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-tabs-x/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\test-project\vendor/kartik-v/yii2-widget-growl/src";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\test-project\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\test-project\vendor/yiisoft/yii2-debug/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\test-project\vendor/kartik-v/yii2-widget-switchinput";}}s:18:"mdmsoft/yii2-admin";a:3:{s:4:"name";s:18:"mdmsoft/yii2-admin";s:7:"version";s:8:"2.12.0.0";s:5:"alias";a:1:{s:10:"@mdm/admin";s:45:"C:\Web\test-project\vendor/mdmsoft/yii2-admin";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\test-project\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\test-project\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:25:"webzop/yii2-notifications";a:4:{s:4:"name";s:25:"webzop/yii2-notifications";s:7:"version";s:7:"0.2.0.0";s:5:"alias";a:1:{s:21:"@webzop/notifications";s:52:"C:\Web\test-project\vendor/webzop/yii2-notifications";}s:9:"bootstrap";s:30:"webzop\notifications\Bootstrap";}s:27:"tuyakhov/yii2-notifications";a:3:{s:4:"name";s:27:"tuyakhov/yii2-notifications";s:7:"version";s:7:"*******";s:5:"alias";a:2:{s:23:"@tuyakhov/notifications";s:58:"C:\Web\test-project\vendor/tuyakhov/yii2-notifications/src";s:29:"@tuyakhov/notifications/tests";s:60:"C:\Web\test-project\vendor/tuyakhov/yii2-notifications/tests";}}}}";s:3:"log";s:25219:"a:1:{s:8:"messages";a:49:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752513973.497444;i:4;a:0:{}i:5;i:2795112;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752513973.497954;i:4;a:0:{}i:5;i:2827416;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752513973.497961;i:4;a:0:{}i:5;i:2828352;}i:3;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752513973.498591;i:4;a:0:{}i:5;i:2861600;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752513973.49952;i:4;a:0:{}i:5;i:2966904;}i:5;a:6:{i:0;s:58:"Bootstrap with webzop\notifications\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752513973.500279;i:4;a:0:{}i:5;i:3034640;}i:6;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752513973.500994;i:4;a:0:{}i:5;i:3123856;}i:7;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1752513973.501008;i:4;a:0:{}i:5;i:3124496;}i:8;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:1752513973.506516;i:4;a:0:{}i:5;i:4119336;}i:9;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.54229;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5440392;}i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=test_project";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:1752513973.542406;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5442512;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.559153;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5499384;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.560594;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5520648;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.571036;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5970488;}i:24;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752513973.598934;i:4;a:0:{}i:5;i:6680104;}i:25;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1752513973.606757;i:4;a:0:{}i:5;i:6939896;}i:34;a:6:{i:0;s:35:"Route requested: 'mock-posts/index'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:1752513973.609536;i:4;a:0:{}i:5;i:6981832;}i:35;a:6:{i:0;s:30:"Route to run: mock-posts/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:1752513973.615373;i:4;a:0:{}i:5;i:7097488;}i:36;a:6:{i:0;s:71:"Running action: frontend\controllers\MockPostsController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1752513973.617175;i:4;a:0:{}i:5;i:7114816;}i:37;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `mock_posts`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.627671;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7281864;}i:40;a:6:{i:0;s:30:"SHOW CREATE TABLE `mock_posts`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.630764;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7296640;}i:43;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'mock_posts' AND `kcu`.`TABLE_NAME` = 'mock_posts'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.632974;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7300288;}i:46;a:6:{i:0;s:130:"GET http://localhost:9000/api/sync?synced=0
Authorization: Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO";i:1;i:4;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1752513973.639058;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:87;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:107;s:8:"function";s:13:"fetchSyncData";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7519208;}i:49;a:6:{i:0;s:41:"SELECT EXISTS(SELECT * FROM `mock_posts`)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.748836;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:110;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:7576608;}i:52;a:6:{i:0;s:122:"GET http://localhost:9000/api/posts
Authorization: Bearer F_RoBKDdxtV8DKPoqNeAqtAN2byl-95-V0LNgkz_W7VtUHSvFMJc0KfZPi8-xfVA";i:1;i:4;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1752513973.750455;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:70;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:362;s:8:"function";s:12:"fetchApiData";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:116;s:8:"function";s:10:"findRecord";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7583432;}i:55;a:6:{i:0;s:199:"PUT http://localhost:9000/api/sync
Authorization: Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO
Content-Type: application/json
Accept: application/json

{"pk":29,"synced":1}";i:1;i:4;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1752513973.852259;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:469;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:117;s:8:"function";s:16:"updateSyncStatus";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7590384;}i:58;a:6:{i:0;s:85:"Sync API response: {"success":false,"message":"No changes made or record not found."}";i:1;i:4;i:2;s:58:"frontend\controllers\MockPostsController::updateSyncStatus";i:3;d:**********.056083;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:472;s:8:"function";s:4:"info";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:117;s:8:"function";s:16:"updateSyncStatus";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7591768;}i:59;a:6:{i:0;s:76:"Rendering view file: C:\Web\test-project\frontend\views\mock-posts\index.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.057788;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:7766320;}i:60;a:6:{i:0;s:24:"Loading module: gridview";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.068523;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-krajee-base\src\Config.php";s:4:"line";i:313;s:8:"function";s:9:"getModule";s:5:"class";s:15:"yii\base\Module";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1255;s:8:"function";s:9:"getModule";s:5:"class";s:18:"kartik\base\Config";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:824;s:8:"function";s:10:"initModule";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:9490456;}i:61;a:6:{i:0;s:33:"SELECT COUNT(*) FROM `mock_posts`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.078013;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10884400;}i:64;a:6:{i:0;s:35:"SELECT * FROM `mock_posts` LIMIT 20";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.083468;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\test-project\frontend\views\mock-posts\index.php";s:4:"line";i:135;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11103488;}i:67;a:6:{i:0;s:72:"Rendering view file: C:\Web\test-project\frontend\views\layouts\main.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.085558;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11130472;}i:68;a:6:{i:0;s:73:"SELECT * FROM `menu_item` WHERE `parent_id` IS NULL ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.086294;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11222792;}i:71;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `menu_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.087016;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11235992;}i:74;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.088621;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11254280;}i:77;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'menu_item' AND `kcu`.`TABLE_NAME` = 'menu_item'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.089267;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11252312;}i:80;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=32 ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.090782;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11265792;}i:83;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=33 ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.091335;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11270624;}i:86;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=28 ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.091811;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11275240;}i:89;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=27 ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.092398;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11279856;}i:92;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=35 ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.092978;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11294696;}i:95;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=40 ORDER BY `sort_order`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.093496;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11302528;}i:98;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `user_notification` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.0958;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11447088;}i:101;a:6:{i:0;s:87:"SELECT * FROM `user_notification` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.096395;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11451984;}i:104;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `user_notification`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.097016;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11464360;}i:107;a:6:{i:0;s:37:"SHOW CREATE TABLE `user_notification`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.098575;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11476376;}i:110;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notification' AND `kcu`.`TABLE_NAME` = 'user_notification'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.099169;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11479280;}i:113;a:6:{i:0;s:92:"Rendering view file: C:\Web\test-project\common\widgets\views\notificationDropdown/index.php";i:1;i:8;i:2;s:25:"yii\base\View::renderFile";i:3;d:**********.101013;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:27;s:8:"function";s:6:"render";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11488376;}i:114;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='mock-posts/index'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.110928;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11662496;}}}";s:9:"profiling";s:40049:"a:3:{s:6:"memory";i:11866808;s:4:"time";d:0.6263139247894287;s:8:"messages";a:60:{i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=test_project";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:1752513973.542445;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5443320;}i:12;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=test_project";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1752513973.557033;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5486624;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.557072;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5486408;}i:14;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.558955;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5498096;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.559214;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5500296;}i:17;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.559876;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5502936;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.560608;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5521688;}i:20;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.561983;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5524216;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.571125;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5970872;}i:23;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.572146;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5973216;}i:38;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `mock_posts`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.62774;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7283152;}i:39;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `mock_posts`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.630661;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7294976;}i:41;a:6:{i:0;s:30:"SHOW CREATE TABLE `mock_posts`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.630795;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7297928;}i:42;a:6:{i:0;s:30:"SHOW CREATE TABLE `mock_posts`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.631658;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7300216;}i:44;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'mock_posts' AND `kcu`.`TABLE_NAME` = 'mock_posts'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.633011;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7301704;}i:45;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'mock_posts' AND `kcu`.`TABLE_NAME` = 'mock_posts'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.635366;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7303936;}i:47;a:6:{i:0;s:130:"GET http://localhost:9000/api/sync?synced=0
Authorization: Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO";i:1;i:80;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1752513973.639091;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:87;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:107;s:8:"function";s:13:"fetchSyncData";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7520392;}i:48;a:6:{i:0;s:130:"GET http://localhost:9000/api/sync?synced=0
Authorization: Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO";i:1;i:96;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1752513973.742331;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:87;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:107;s:8:"function";s:13:"fetchSyncData";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7524272;}i:50;a:6:{i:0;s:41:"SELECT EXISTS(SELECT * FROM `mock_posts`)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.748903;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:110;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:7577520;}i:51;a:6:{i:0;s:41:"SELECT EXISTS(SELECT * FROM `mock_posts`)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.750223;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:110;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:7578416;}i:53;a:6:{i:0;s:122:"GET http://localhost:9000/api/posts
Authorization: Bearer F_RoBKDdxtV8DKPoqNeAqtAN2byl-95-V0LNgkz_W7VtUHSvFMJc0KfZPi8-xfVA";i:1;i:80;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1752513973.750464;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:70;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:362;s:8:"function";s:12:"fetchApiData";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:116;s:8:"function";s:10:"findRecord";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7584992;}i:54;a:6:{i:0;s:122:"GET http://localhost:9000/api/posts
Authorization: Bearer F_RoBKDdxtV8DKPoqNeAqtAN2byl-95-V0LNgkz_W7VtUHSvFMJc0KfZPi8-xfVA";i:1;i:96;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1752513973.851987;i:4;a:3:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:70;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:362;s:8:"function";s:12:"fetchApiData";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:116;s:8:"function";s:10:"findRecord";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7590624;}i:56;a:6:{i:0;s:199:"PUT http://localhost:9000/api/sync
Authorization: Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO
Content-Type: application/json
Accept: application/json

{"pk":29,"synced":1}";i:1;i:80;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:1752513973.852269;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:469;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:117;s:8:"function";s:16:"updateSyncStatus";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7591568;}i:57;a:6:{i:0;s:199:"PUT http://localhost:9000/api/sync
Authorization: Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO
Content-Type: application/json
Accept: application/json

{"pk":29,"synced":1}";i:1;i:96;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:**********.056019;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:469;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:117;s:8:"function";s:16:"updateSyncStatus";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7595416;}i:62;a:6:{i:0;s:33:"SELECT COUNT(*) FROM `mock_posts`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.078033;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10886064;}i:63;a:6:{i:0;s:33:"SELECT COUNT(*) FROM `mock_posts`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.080031;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10887688;}i:65;a:6:{i:0;s:35:"SELECT * FROM `mock_posts` LIMIT 20";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.083489;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\test-project\frontend\views\mock-posts\index.php";s:4:"line";i:135;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11106432;}i:66;a:6:{i:0;s:35:"SELECT * FROM `mock_posts` LIMIT 20";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.08412;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\test-project\frontend\views\mock-posts\index.php";s:4:"line";i:135;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11109592;}i:69;a:6:{i:0;s:73:"SELECT * FROM `menu_item` WHERE `parent_id` IS NULL ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.086306;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11224080;}i:70;a:6:{i:0;s:73:"SELECT * FROM `menu_item` WHERE `parent_id` IS NULL ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.086974;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11231432;}i:72;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `menu_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.087025;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11237280;}i:73;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `menu_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.088594;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11252616;}i:75;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.08863;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11255568;}i:76;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.089166;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11258240;}i:78;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'menu_item' AND `kcu`.`TABLE_NAME` = 'menu_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.089282;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11253728;}i:79;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'menu_item' AND `kcu`.`TABLE_NAME` = 'menu_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.090616;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11256656;}i:81;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=32 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.090792;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11267296;}i:82;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=32 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.091269;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11269032;}i:84;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=33 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.091344;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11272128;}i:85;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=33 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.091726;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11273864;}i:87;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=28 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.091822;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11276744;}i:88;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=28 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.092332;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11278480;}i:90;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=27 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.092407;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11281360;}i:91;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=27 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.092864;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11286992;}i:93;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=35 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.092993;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11296200;}i:94;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=35 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.093421;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11300928;}i:96;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=40 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.093504;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11304032;}i:97;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=40 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.09402;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11308744;}i:99;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `user_notification` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.095812;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11448864;}i:100;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `user_notification` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.096327;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11450560;}i:102;a:6:{i:0;s:87:"SELECT * FROM `user_notification` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.096404;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11453864;}i:103;a:6:{i:0;s:87:"SELECT * FROM `user_notification` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.096865;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11460688;}i:105;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `user_notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.097059;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11466024;}i:106;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `user_notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.098511;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11474328;}i:108;a:6:{i:0;s:37:"SHOW CREATE TABLE `user_notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.098595;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11478040;}i:109;a:6:{i:0;s:37:"SHOW CREATE TABLE `user_notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.099078;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11480968;}i:111;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notification' AND `kcu`.`TABLE_NAME` = 'user_notification'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.09918;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11481072;}i:112;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notification' AND `kcu`.`TABLE_NAME` = 'user_notification'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.100743;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11484920;}i:115;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='mock-posts/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.11098;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11663624;}i:116;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='mock-posts/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.111683;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11664776;}}}";s:2:"db";s:34613:"a:1:{s:8:"messages";a:52:{i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.557072;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5486408;}i:14;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.558955;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5498096;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.559214;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5500296;}i:17;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.559876;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5502936;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.560608;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5521688;}i:20;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.561983;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5524216;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.571125;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5970872;}i:23;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.572146;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5973216;}i:38;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `mock_posts`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.62774;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7283152;}i:39;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `mock_posts`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.630661;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7294976;}i:41;a:6:{i:0;s:30:"SHOW CREATE TABLE `mock_posts`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.630795;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7297928;}i:42;a:6:{i:0;s:30:"SHOW CREATE TABLE `mock_posts`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.631658;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7300216;}i:44;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'mock_posts' AND `kcu`.`TABLE_NAME` = 'mock_posts'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.633011;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7301704;}i:45;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'mock_posts' AND `kcu`.`TABLE_NAME` = 'mock_posts'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.635366;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7303936;}i:50;a:6:{i:0;s:41:"SELECT EXISTS(SELECT * FROM `mock_posts`)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.748903;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:110;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:7577520;}i:51;a:6:{i:0;s:41:"SELECT EXISTS(SELECT * FROM `mock_posts`)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1752513973.750223;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:110;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:7578416;}i:62;a:6:{i:0;s:33:"SELECT COUNT(*) FROM `mock_posts`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.078033;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10886064;}i:63;a:6:{i:0;s:33:"SELECT COUNT(*) FROM `mock_posts`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.080031;i:4;a:3:{i:0;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:1537;s:8:"function";s:13:"getTotalCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridViewTrait.php";s:4:"line";i:886;s:8:"function";s:14:"initToggleData";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:109;s:8:"function";s:15:"prepareGridView";s:5:"class";s:20:"kartik\grid\GridView";s:4:"type";s:2:"->";}}i:5;i:10887688;}i:65;a:6:{i:0;s:35:"SELECT * FROM `mock_posts` LIMIT 20";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.083489;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\test-project\frontend\views\mock-posts\index.php";s:4:"line";i:135;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11106432;}i:66;a:6:{i:0;s:35:"SELECT * FROM `mock_posts` LIMIT 20";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.08412;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:209;s:8:"function";s:8:"getCount";s:5:"class";s:25:"yii\data\BaseDataProvider";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:62:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src\GridView.php";s:4:"line";i:112;s:8:"function";s:3:"run";s:5:"class";s:17:"yii\grid\GridView";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:55:"C:\Web\test-project\frontend\views\mock-posts\index.php";s:4:"line";i:135;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}}i:5;i:11109592;}i:69;a:6:{i:0;s:73:"SELECT * FROM `menu_item` WHERE `parent_id` IS NULL ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.086306;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11224080;}i:70;a:6:{i:0;s:73:"SELECT * FROM `menu_item` WHERE `parent_id` IS NULL ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.086974;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11231432;}i:72;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `menu_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.087025;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11237280;}i:73;a:6:{i:0;s:34:"SHOW FULL COLUMNS FROM `menu_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.088594;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11252616;}i:75;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.08863;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11255568;}i:76;a:6:{i:0;s:29:"SHOW CREATE TABLE `menu_item`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.089166;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11258240;}i:78;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'menu_item' AND `kcu`.`TABLE_NAME` = 'menu_item'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.089282;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11253728;}i:79;a:6:{i:0;s:774:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'menu_item' AND `kcu`.`TABLE_NAME` = 'menu_item'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.090616;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:19;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11256656;}i:81;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=32 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.090792;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11267296;}i:82;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=32 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.091269;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11269032;}i:84;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=33 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.091344;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11272128;}i:85;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=33 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.091726;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11273864;}i:87;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=28 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.091822;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11276744;}i:88;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=28 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.092332;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11278480;}i:90;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=27 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.092407;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11281360;}i:91;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=27 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.092864;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11286992;}i:93;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=35 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.092993;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11296200;}i:94;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=35 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.093421;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11300928;}i:96;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=40 ORDER BY `sort_order`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.093504;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11304032;}i:97;a:6:{i:0;s:68:"SELECT * FROM `menu_item` WHERE `parent_id`=40 ORDER BY `sort_order`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.09402;i:4;a:2:{i:0;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:20;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11308744;}i:99;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `user_notification` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.095812;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11448864;}i:100;a:6:{i:0;s:78:"SELECT COUNT(*) FROM `user_notification` WHERE (`user_id`=1) AND (`is_read`=0)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.096327;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:19;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11450560;}i:102;a:6:{i:0;s:87:"SELECT * FROM `user_notification` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.096404;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11453864;}i:103;a:6:{i:0;s:87:"SELECT * FROM `user_notification` WHERE `user_id`=1 ORDER BY `created_at` DESC LIMIT 10";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.096865;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11460688;}i:105;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `user_notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.097059;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11466024;}i:106;a:6:{i:0;s:42:"SHOW FULL COLUMNS FROM `user_notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.098511;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11474328;}i:108;a:6:{i:0;s:37:"SHOW CREATE TABLE `user_notification`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.098595;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11478040;}i:109;a:6:{i:0;s:37:"SHOW CREATE TABLE `user_notification`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.099078;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11480968;}i:111;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notification' AND `kcu`.`TABLE_NAME` = 'user_notification'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.09918;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11481072;}i:112;a:6:{i:0;s:790:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user_notification' AND `kcu`.`TABLE_NAME` = 'user_notification'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.100743;i:4;a:3:{i:0;a:5:{s:4:"file";s:59:"C:\Web\test-project\common\widgets\NotificationDropdown.php";s:4:"line";i:25;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:51:"C:\Web\test-project\frontend\views\layouts\main.php";s:4:"line";i:126;s:8:"function";s:6:"widget";s:5:"class";s:15:"yii\base\Widget";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:131;s:8:"function";s:6:"render";s:5:"class";s:19:"yii\base\Controller";s:4:"type";s:2:"->";}}i:5;i:11484920;}i:115;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='mock-posts/index'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.11098;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11663624;}i:116;a:6:{i:0;s:69:"SELECT * FROM `notification_trigger` WHERE `route`='mock-posts/index'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.111683;i:4;a:1:{i:0;a:5:{s:4:"file";s:62:"C:\Web\test-project\common\components\NotificationListener.php";s:4:"line";i:23;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}}i:5;i:11664776;}}}";s:5:"event";s:42187:"a:229:{i:0;a:5:{s:4:"time";d:1752513973.531542;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:1752513973.557016;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:1752513973.573309;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:1752513973.573383;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:1752513973.590924;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:1752513973.608004;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:1752513973.616057;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:7;a:5:{s:4:"time";d:1752513973.617117;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"frontend\controllers\MockPostsController";}i:8;a:5:{s:4:"time";d:1752513973.619809;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"common\models\search\MockPostsSearch";}i:9;a:5:{s:4:"time";d:1752513973.61989;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:10;a:5:{s:4:"time";d:1752513973.62363;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"common\models\search\MockPostsSearch";}i:11;a:5:{s:4:"time";d:1752513973.635498;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"common\models\search\MockPostsSearch";}i:12;a:5:{s:4:"time";d:1752513973.637136;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:13;a:5:{s:4:"time";d:1752513973.637142;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:14;a:5:{s:4:"time";d:1752513973.744971;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:15;a:5:{s:4:"time";d:1752513973.745011;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:16;a:5:{s:4:"time";d:1752513973.748564;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:17;a:5:{s:4:"time";d:1752513973.750401;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:18;a:5:{s:4:"time";d:1752513973.75041;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:19;a:5:{s:4:"time";d:1752513973.85206;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:20;a:5:{s:4:"time";d:1752513973.852072;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:21;a:5:{s:4:"time";d:1752513973.852212;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:22;a:5:{s:4:"time";d:1752513973.852218;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:23;a:5:{s:4:"time";d:**********.056071;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:24;a:5:{s:4:"time";d:**********.056077;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:25;a:5:{s:4:"time";d:**********.057783;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:26;a:5:{s:4:"time";d:**********.067707;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:27;a:5:{s:4:"time";d:**********.067818;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:28;a:5:{s:4:"time";d:**********.067827;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:29;a:5:{s:4:"time";d:**********.069404;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:30;a:5:{s:4:"time";d:**********.069415;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:31;a:5:{s:4:"time";d:**********.069421;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:32;a:5:{s:4:"time";d:**********.069443;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:33;a:5:{s:4:"time";d:**********.072112;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:34;a:5:{s:4:"time";d:**********.072129;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:35;a:5:{s:4:"time";d:**********.072138;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:36;a:5:{s:4:"time";d:**********.072142;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:37;a:5:{s:4:"time";d:**********.072146;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:38;a:5:{s:4:"time";d:**********.072149;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:39;a:5:{s:4:"time";d:**********.072157;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:40;a:5:{s:4:"time";d:**********.07216;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:41;a:5:{s:4:"time";d:**********.072165;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:42;a:5:{s:4:"time";d:**********.072168;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:43;a:5:{s:4:"time";d:**********.072171;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:44;a:5:{s:4:"time";d:**********.072174;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:45;a:5:{s:4:"time";d:**********.072178;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:46;a:5:{s:4:"time";d:**********.072181;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:47;a:5:{s:4:"time";d:**********.072185;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:48;a:5:{s:4:"time";d:**********.072188;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:49;a:5:{s:4:"time";d:**********.072191;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:50;a:5:{s:4:"time";d:**********.072195;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:51;a:5:{s:4:"time";d:**********.072202;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:52;a:5:{s:4:"time";d:**********.072205;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:53;a:5:{s:4:"time";d:**********.072209;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:54;a:5:{s:4:"time";d:**********.072212;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:55;a:5:{s:4:"time";d:**********.072215;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:56;a:5:{s:4:"time";d:**********.072219;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:57;a:5:{s:4:"time";d:**********.072223;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:58;a:5:{s:4:"time";d:**********.072226;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:59;a:5:{s:4:"time";d:**********.072229;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:60;a:5:{s:4:"time";d:**********.072233;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:61;a:5:{s:4:"time";d:**********.072236;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:62;a:5:{s:4:"time";d:**********.072239;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:63;a:5:{s:4:"time";d:**********.072242;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:64;a:5:{s:4:"time";d:**********.072245;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:65;a:5:{s:4:"time";d:**********.072249;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:66;a:5:{s:4:"time";d:**********.072252;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:67;a:5:{s:4:"time";d:**********.072256;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:68;a:5:{s:4:"time";d:**********.072259;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:69;a:5:{s:4:"time";d:**********.072262;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:70;a:5:{s:4:"time";d:**********.072265;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:71;a:5:{s:4:"time";d:**********.072268;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:72;a:5:{s:4:"time";d:**********.072272;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:73;a:5:{s:4:"time";d:**********.072275;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:74;a:5:{s:4:"time";d:**********.073466;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:75;a:5:{s:4:"time";d:**********.073476;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:76;a:5:{s:4:"time";d:**********.073738;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:77;a:5:{s:4:"time";d:**********.073747;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:78;a:5:{s:4:"time";d:**********.073751;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:79;a:5:{s:4:"time";d:**********.073757;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:80;a:5:{s:4:"time";d:**********.074191;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:81;a:5:{s:4:"time";d:**********.075289;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:82;a:5:{s:4:"time";d:**********.075652;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:83;a:5:{s:4:"time";d:**********.07645;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:84;a:5:{s:4:"time";d:**********.076456;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:85;a:5:{s:4:"time";d:**********.07646;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:86;a:5:{s:4:"time";d:**********.076477;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:87;a:5:{s:4:"time";d:**********.076826;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:88;a:5:{s:4:"time";d:**********.076831;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:89;a:5:{s:4:"time";d:**********.077017;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:90;a:5:{s:4:"time";d:**********.077021;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:91;a:5:{s:4:"time";d:**********.077024;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"yii2ajaxcrud\ajaxcrud\BulkButtonWidget";}i:92;a:5:{s:4:"time";d:**********.077063;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:93;a:5:{s:4:"time";d:**********.077928;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:94;a:5:{s:4:"time";d:**********.07795;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:95;a:5:{s:4:"time";d:**********.077956;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:96;a:5:{s:4:"time";d:**********.07796;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:97;a:5:{s:4:"time";d:**********.077968;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:98;a:5:{s:4:"time";d:**********.080906;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"kartik\bs5dropdown\ButtonDropdown";}i:99;a:5:{s:4:"time";d:**********.080915;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"kartik\bs5dropdown\ButtonDropdown";}i:100;a:5:{s:4:"time";d:**********.081523;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\Button";}i:101;a:5:{s:4:"time";d:**********.081532;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\Button";}i:102;a:5:{s:4:"time";d:**********.081556;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\Button";}i:103;a:5:{s:4:"time";d:**********.082349;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:104;a:5:{s:4:"time";d:**********.082362;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:105;a:5:{s:4:"time";d:**********.083077;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:106;a:5:{s:4:"time";d:**********.083093;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:33:"kartik\bs5dropdown\ButtonDropdown";}i:107;a:5:{s:4:"time";d:**********.083178;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:108;a:5:{s:4:"time";d:**********.083186;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:109;a:5:{s:4:"time";d:**********.083232;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\dialog\Dialog";}i:110;a:5:{s:4:"time";d:**********.083366;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:111;a:5:{s:4:"time";d:**********.083422;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"common\models\MockPosts";}i:112;a:5:{s:4:"time";d:**********.084147;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"common\models\MockPosts";}i:113;a:5:{s:4:"time";d:**********.084169;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"common\models\MockPosts";}i:114;a:5:{s:4:"time";d:**********.084647;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:115;a:5:{s:4:"time";d:**********.08469;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:116;a:5:{s:4:"time";d:**********.084693;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:117;a:5:{s:4:"time";d:**********.084697;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\widgets\LinkPager";}i:118;a:5:{s:4:"time";d:**********.084719;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:119;a:5:{s:4:"time";d:**********.084741;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\widgets\Pjax";}i:120;a:5:{s:4:"time";d:**********.084744;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"kartik\grid\GridView";}i:121;a:5:{s:4:"time";d:**********.085185;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:122;a:5:{s:4:"time";d:**********.085455;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:123;a:5:{s:4:"time";d:**********.085477;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"yii\bootstrap5\Modal";}i:124;a:5:{s:4:"time";d:**********.085496;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:125;a:5:{s:4:"time";d:**********.085556;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:126;a:5:{s:4:"time";d:**********.086247;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:127;a:5:{s:4:"time";d:**********.086992;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:128;a:5:{s:4:"time";d:**********.090663;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:129;a:5:{s:4:"time";d:**********.090676;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:130;a:5:{s:4:"time";d:**********.090683;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:131;a:5:{s:4:"time";d:**********.09069;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:132;a:5:{s:4:"time";d:**********.090696;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:133;a:5:{s:4:"time";d:**********.090704;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:134;a:5:{s:4:"time";d:**********.090706;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:135;a:5:{s:4:"time";d:**********.090708;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:136;a:5:{s:4:"time";d:**********.09071;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:137;a:5:{s:4:"time";d:**********.090712;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:138;a:5:{s:4:"time";d:**********.090714;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:139;a:5:{s:4:"time";d:**********.090725;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:140;a:5:{s:4:"time";d:**********.091294;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:141;a:5:{s:4:"time";d:**********.091761;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:142;a:5:{s:4:"time";d:**********.092358;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:143;a:5:{s:4:"time";d:**********.092883;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:144;a:5:{s:4:"time";d:**********.092903;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:145;a:5:{s:4:"time";d:**********.092912;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:146;a:5:{s:4:"time";d:**********.092919;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:147;a:5:{s:4:"time";d:**********.092927;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:148;a:5:{s:4:"time";d:**********.092929;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:149;a:5:{s:4:"time";d:**********.092931;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:150;a:5:{s:4:"time";d:**********.092933;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:151;a:5:{s:4:"time";d:**********.092944;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:152;a:5:{s:4:"time";d:**********.093431;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:153;a:5:{s:4:"time";d:**********.093443;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:154;a:5:{s:4:"time";d:**********.09345;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:155;a:5:{s:4:"time";d:**********.093457;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:156;a:5:{s:4:"time";d:**********.093459;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:157;a:5:{s:4:"time";d:**********.093461;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:158;a:5:{s:4:"time";d:**********.09347;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:159;a:5:{s:4:"time";d:**********.094032;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:160;a:5:{s:4:"time";d:**********.094045;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:161;a:5:{s:4:"time";d:**********.094054;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:162;a:5:{s:4:"time";d:**********.094061;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:163;a:5:{s:4:"time";d:**********.094064;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:164;a:5:{s:4:"time";d:**********.094066;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"common\models\MenuItem";}i:165;a:5:{s:4:"time";d:**********.09413;s:4:"name";s:9:"beginPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:166;a:5:{s:4:"time";d:**********.094179;s:4:"name";s:9:"beginBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:167;a:5:{s:4:"time";d:**********.094632;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\NavBar";}i:168;a:5:{s:4:"time";d:**********.095041;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\bootstrap5\Nav";}i:169;a:5:{s:4:"time";d:**********.095058;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\bootstrap5\Nav";}i:170;a:5:{s:4:"time";d:**********.095138;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:171;a:5:{s:4:"time";d:**********.095142;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:172;a:5:{s:4:"time";d:**********.095189;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:173;a:5:{s:4:"time";d:**********.095213;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:174;a:5:{s:4:"time";d:**********.095217;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:175;a:5:{s:4:"time";d:**********.095249;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:176;a:5:{s:4:"time";d:**********.095269;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:177;a:5:{s:4:"time";d:**********.095271;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:178;a:5:{s:4:"time";d:**********.095306;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\bootstrap5\Dropdown";}i:179;a:5:{s:4:"time";d:**********.095317;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\bootstrap5\Nav";}i:180;a:5:{s:4:"time";d:**********.095555;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"common\widgets\NotificationDropdown";}i:181;a:5:{s:4:"time";d:**********.095559;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"common\widgets\NotificationDropdown";}i:182;a:5:{s:4:"time";d:**********.095758;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:183;a:5:{s:4:"time";d:**********.096357;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:184;a:5:{s:4:"time";d:**********.096933;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:185;a:5:{s:4:"time";d:**********.100792;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:186;a:5:{s:4:"time";d:**********.100803;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:187;a:5:{s:4:"time";d:**********.10081;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:188;a:5:{s:4:"time";d:**********.100816;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:189;a:5:{s:4:"time";d:**********.100822;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:190;a:5:{s:4:"time";d:**********.100827;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:191;a:5:{s:4:"time";d:**********.100832;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:192;a:5:{s:4:"time";d:**********.100837;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:193;a:5:{s:4:"time";d:**********.100844;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:194;a:5:{s:4:"time";d:**********.100846;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:195;a:5:{s:4:"time";d:**********.100848;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:196;a:5:{s:4:"time";d:**********.10085;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:197;a:5:{s:4:"time";d:**********.100852;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:198;a:5:{s:4:"time";d:**********.100854;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:199;a:5:{s:4:"time";d:**********.100856;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:200;a:5:{s:4:"time";d:**********.100858;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:201;a:5:{s:4:"time";d:**********.10086;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:30:"common\models\UserNotification";}i:202;a:5:{s:4:"time";d:**********.10101;s:4:"name";s:12:"beforeRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:203;a:5:{s:4:"time";d:**********.101443;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:204;a:5:{s:4:"time";d:**********.101451;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:35:"common\widgets\NotificationDropdown";}i:205;a:5:{s:4:"time";d:**********.101479;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:206;a:5:{s:4:"time";d:**********.101491;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:207;a:5:{s:4:"time";d:**********.101536;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"kartik\bs5dropdown\Dropdown";}i:208;a:5:{s:4:"time";d:**********.101543;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\NavBar";}i:209;a:5:{s:4:"time";d:**********.101549;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\bootstrap5\NavBar";}i:210;a:5:{s:4:"time";d:**********.102014;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"dominus77\sweetalert2\Alert";}i:211;a:5:{s:4:"time";d:**********.102019;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"dominus77\sweetalert2\Alert";}i:212;a:5:{s:4:"time";d:**********.102029;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:27:"dominus77\sweetalert2\Alert";}i:213;a:5:{s:4:"time";d:**********.102417;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:214;a:5:{s:4:"time";d:**********.102423;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:215;a:5:{s:4:"time";d:**********.10246;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"yii\bootstrap5\Breadcrumbs";}i:216;a:5:{s:4:"time";d:**********.103155;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"common\widgets\Alert";}i:217;a:5:{s:4:"time";d:**********.103191;s:4:"name";s:9:"beforeRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"common\widgets\Alert";}i:218;a:5:{s:4:"time";d:**********.103262;s:4:"name";s:8:"afterRun";s:5:"class";s:20:"yii\base\WidgetEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"common\widgets\Alert";}i:219;a:5:{s:4:"time";d:**********.108134;s:4:"name";s:7:"endBody";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:220;a:5:{s:4:"time";d:**********.109014;s:4:"name";s:7:"endPage";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:221;a:5:{s:4:"time";d:**********.109468;s:4:"name";s:11:"afterRender";s:5:"class";s:18:"yii\base\ViewEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\View";}i:222;a:5:{s:4:"time";d:**********.109509;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"frontend\controllers\MockPostsController";}i:223;a:5:{s:4:"time";d:**********.110689;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:224;a:5:{s:4:"time";d:**********.1117;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:225;a:5:{s:4:"time";d:**********.11171;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:226;a:5:{s:4:"time";d:**********.111715;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:227;a:5:{s:4:"time";d:**********.112145;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:228;a:5:{s:4:"time";d:**********.112423;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:92:"a:3:{s:5:"start";d:1752513973.488907;s:3:"end";d:**********.116482;s:6:"memory";i:11866808;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:1667:"a:3:{s:8:"messages";a:8:{i:26;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752513973.609366;i:4;a:0:{}i:5;i:6977728;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752513973.609399;i:4;a:0:{}i:5;i:6978320;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752513973.609419;i:4;a:0:{}i:5;i:6978912;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752513973.609434;i:4;a:0:{}i:5;i:6979504;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752513973.60945;i:4;a:0:{}i:5;i:6980096;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET api/<resource:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752513973.609481;i:4;a:0:{}i:5;i:6980744;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:26:"PUT api/<resource:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752513973.609501;i:4;a:0:{}i:5;i:6981392;}i:33;a:6:{i:0;s:55:"No matching URL rules. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:1752513973.609511;i:4;a:0:{}i:5;i:6982248;}}s:5:"route";s:16:"mock-posts/index";s:6:"action";s:55:"frontend\controllers\MockPostsController::actionIndex()";}";s:7:"request";s:10848:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:20:{s:12:"content-type";s:0:"";s:14:"content-length";s:1:"0";s:14:"x-original-url";s:17:"/mock-posts/index";s:14:"sec-fetch-dest";s:8:"document";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:11:"same-origin";s:25:"upgrade-insecure-requests";s:1:"1";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:64:""Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:7:"referer";s:38:"http://localhost:9000/mock-posts/index";s:4:"host";s:14:"localhost:9000";s:6:"cookie";s:954:"_identity-frontend=1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; advanced-frontend=lj75dob3daup08nk4a8tootgbt; _csrf-frontend=987794102ea9939de322b56534be5c83dc38b85b3c5875dcd6a75e6555987485a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22mGPFQ_JwvodttQ9_4GPme5P-ekoMqTgH%22%3B%7D; advanced-backend-test=0e3n9oj5c286gmtljdrc2j5hu5; _csrf-backend=662260930e32dfab483a7cb4ebeea2fc94ee5f0fd8822614b5c1202ccdd85223a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22uC8kUI6_jtEX6ckmjU3H4rhhguwLkXAD%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"connection";s:10:"keep-alive";s:13:"cache-control";s:9:"max-age=0";}s:15:"responseHeaders";a:9:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-Type";s:24:"text/html; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"68753db5927fd";s:16:"X-Debug-Duration";s:3:"624";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=68753db5927fd";s:10:"Set-Cookie";s:313:"_identity-frontend=1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; expires=Wed, 13 Aug 2025 17:26:13 GMT; Max-Age=2591999; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:16:"mock-posts/index";s:6:"action";s:55:"frontend\controllers\MockPostsController::actionIndex()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:108:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-164134d3-e58a-4807-90b8-58fadc75ad36";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Test Project\Test Project.config";s:11:"APP_POOL_ID";s:12:"Test Project";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:462:"C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:10:"/index.php";s:3:"URL";s:10:"/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"9000";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:10:"/index.php";s:15:"SCRIPT_FILENAME";s:42:"C:\Web\test-project\frontend\web\index.php";s:11:"REQUEST_URI";s:17:"/mock-posts/index";s:14:"REQUEST_METHOD";s:3:"GET";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"49684";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:0:"";s:15:"PATH_TRANSLATED";s:42:"C:\Web\test-project\frontend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/4";s:13:"INSTANCE_NAME";s:12:"TEST PROJECT";s:11:"INSTANCE_ID";s:1:"4";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\test-project\frontend\web";s:12:"CONTENT_TYPE";s:0:"";s:14:"CONTENT_LENGTH";s:1:"0";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:33:"C:\Web\test-project\frontend\web\";s:12:"APPL_MD_PATH";s:16:"/LM/W3SVC/4/ROOT";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:13:"UNENCODED_URL";s:17:"/mock-posts/index";s:19:"IIS_WasUrlRewritten";s:1:"1";s:19:"HTTP_X_ORIGINAL_URL";s:17:"/mock-posts/index";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:64:""Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:12:"HTTP_REFERER";s:38:"http://localhost:9000/mock-posts/index";s:9:"HTTP_HOST";s:14:"localhost:9000";s:11:"HTTP_COOKIE";s:954:"_identity-frontend=1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; advanced-frontend=lj75dob3daup08nk4a8tootgbt; _csrf-frontend=987794102ea9939de322b56534be5c83dc38b85b3c5875dcd6a75e6555987485a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22mGPFQ_JwvodttQ9_4GPme5P-ekoMqTgH%22%3B%7D; advanced-backend-test=0e3n9oj5c286gmtljdrc2j5hu5; _csrf-backend=662260930e32dfab483a7cb4ebeea2fc94ee5f0fd8822614b5c1202ccdd85223a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22uC8kUI6_jtEX6ckmjU3H4rhhguwLkXAD%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:19:"HTTP_CONTENT_LENGTH";s:1:"0";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:18:"HTTP_CACHE_CONTROL";s:9:"max-age=0";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:1752513973.474883;s:12:"REQUEST_TIME";i:1752513973;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:7:{s:18:"_identity-frontend";s:158:"1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[1,"RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo",2592000]";}";s:16:"sidebar-collapse";s:5:"false";s:17:"_identity-backend";s:157:"946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo",2592000]";}";s:17:"advanced-frontend";s:26:"lj75dob3daup08nk4a8tootgbt";s:14:"_csrf-frontend";s:140:"987794102ea9939de322b56534be5c83dc38b85b3c5875dcd6a75e6555987485a:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"mGPFQ_JwvodttQ9_4GPme5P-ekoMqTgH";}";s:21:"advanced-backend-test";s:26:"0e3n9oj5c286gmtljdrc2j5hu5";s:13:"_csrf-backend";s:139:"662260930e32dfab483a7cb4ebeea2fc94ee5f0fd8822614b5c1202ccdd85223a:2:{i:0;s:13:"_csrf-backend";i:1;s:32:"uC8kUI6_jtEX6ckmjU3H4rhhguwLkXAD";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";s:32:"RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo";}}";s:4:"user";s:2311:"a:5:{s:2:"id";i:1;s:8:"identity";a:11:{s:2:"id";s:1:"1";s:8:"username";s:8:"'qwerty'";s:8:"auth_key";s:34:"'RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo'";s:13:"password_hash";s:62:"'$2y$13$PvAUhC2Ak1Dg55cbPHYsDu8KTgzGOeu08y2SUHvEoYbw7Xqvr8wMG'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1746551344";s:10:"updated_at";s:10:"1746551344";s:18:"verification_token";s:45:"'HyyhnMnjystP5EIGPwdessQ_HqW8DtaX_1746551344'";s:7:"role_id";s:1:"1";}s:10:"attributes";a:11:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:13:"Password Hash";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:7:"Role Id";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-2";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-3";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:9602:"a:17:{s:31:"yii2ajaxcrud\ajaxcrud\CrudAsset";a:9:{s:10:"sourcePath";s:64:"C:\Web\test-project\vendor\biladina\yii2-ajaxcrud-bs4\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\827c4fee";s:7:"baseUrl";s:16:"/assets/827c4fee";s:7:"depends";a:5:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:35:"yii\bootstrap5\BootstrapPluginAsset";i:3;s:25:"kartik\grid\GridViewAsset";i:4;s:38:"yii2ajaxcrud\ajaxcrud\FontAwesomeAsset";}s:2:"js";a:2:{i:0;s:14:"ModalRemote.js";i:1;s:11:"ajaxcrud.js";}s:3:"css";a:1:{i:0;s:12:"ajaxcrud.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:16:"yii\web\YiiAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\test-project\vendor\yiisoft\yii2/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\215e7eb0";s:7:"baseUrl";s:16:"/assets/215e7eb0";s:7:"depends";a:1:{i:0;s:19:"yii\web\JqueryAsset";}s:2:"js";a:1:{i:0;s:6:"yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:19:"yii\web\JqueryAsset";a:9:{s:10:"sourcePath";s:50:"C:\Web\test-project/vendor/bower-asset/jquery/dist";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\d95451c1";s:7:"baseUrl";s:16:"/assets/d95451c1";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:9:"jquery.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:29:"yii\bootstrap5\BootstrapAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\test-project/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\f9baa076";s:7:"baseUrl";s:16:"/assets/f9baa076";s:7:"depends";a:0:{}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:17:"css/bootstrap.css";}s:9:"jsOptions";a:1:{s:8:"position";i:1;}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:35:"yii\bootstrap5\BootstrapPluginAsset";a:9:{s:10:"sourcePath";s:53:"C:\Web\test-project/vendor/bower-asset/bootstrap/dist";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\f9baa076";s:7:"baseUrl";s:16:"/assets/f9baa076";s:7:"depends";a:1:{i:0;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap.bundle.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:25:"kartik\grid\GridViewAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\8db62d20";s:7:"baseUrl";s:16:"/assets/8db62d20";s:7:"depends";a:4:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:22:"yii\grid\GridViewAsset";i:2;s:16:"yii\web\YiiAsset";i:3;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:0:{}s:3:"css";a:1:{i:0;s:15:"css/kv-grid.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:25:"kartik\dialog\DialogAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\test-project\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\eb409b32";s:7:"baseUrl";s:16:"/assets/eb409b32";s:7:"depends";a:1:{i:0;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:12:"js/dialog.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:1:{s:8:"position";i:1;}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:22:"yii\grid\GridViewAsset";a:9:{s:10:"sourcePath";s:46:"C:\Web\test-project\vendor\yiisoft\yii2/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\215e7eb0";s:7:"baseUrl";s:16:"/assets/215e7eb0";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:15:"yii.gridView.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:38:"yii2ajaxcrud\ajaxcrud\FontAwesomeAsset";a:9:{s:10:"sourcePath";s:51:"C:\Web\test-project/vendor/fortawesome/font-awesome";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\d7c10c94";s:7:"baseUrl";s:16:"/assets/d7c10c94";s:7:"depends";a:0:{}s:2:"js";a:1:{i:0;s:13:"js/all.min.js";}s:3:"css";a:1:{i:0;s:15:"css/all.min.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:31:"kartik\grid\CheckboxColumnAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\8db62d20";s:7:"baseUrl";s:16:"/assets/8db62d20";s:7:"depends";a:3:{i:0;s:25:"kartik\grid\GridViewAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:22:"js/kv-grid-checkbox.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:34:"kartik\dialog\DialogBootstrapAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\test-project\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\eb409b32";s:7:"baseUrl";s:16:"/assets/eb409b32";s:7:"depends";a:4:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";i:3;s:35:"yii\bootstrap5\BootstrapPluginAsset";}s:2:"js";a:1:{i:0;s:22:"js/bootstrap-dialog.js";}s:3:"css";a:1:{i:0;s:28:"css/bootstrap-dialog-bs4.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:1;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:28:"kartik\dialog\DialogYiiAsset";a:17:{s:10:"sourcePath";s:58:"C:\Web\test-project\vendor\kartik-v\yii2-dialog\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\eb409b32";s:7:"baseUrl";s:16:"/assets/eb409b32";s:7:"depends";a:3:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:16:"js/dialog-yii.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:27:"kartik\grid\GridExportAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\8db62d20";s:7:"baseUrl";s:16:"/assets/8db62d20";s:7:"depends";a:3:{i:0;s:25:"kartik\dialog\DialogAsset";i:1;s:16:"yii\web\YiiAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:20:"js/kv-grid-export.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:34:"kartik\grid\GridResizeColumnsAsset";a:17:{s:10:"sourcePath";s:56:"C:\Web\test-project\vendor\kartik-v\yii2-grid\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\8db62d20";s:7:"baseUrl";s:16:"/assets/8db62d20";s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:25:"kartik\grid\GridViewAsset";i:2;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:29:"js/jquery.resizableColumns.js";}s:3:"css";a:1:{i:0;s:31:"css/jquery.resizableColumns.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:0;s:9:"bsVersion";N;s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:21:"yii\widgets\PjaxAsset";a:9:{s:10:"sourcePath";s:48:"C:\Web\test-project/vendor/bower-asset/yii2-pjax";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\71798dde";s:7:"baseUrl";s:16:"/assets/71798dde";s:7:"depends";a:1:{i:0;s:16:"yii\web\YiiAsset";}s:2:"js";a:1:{i:0;s:14:"jquery.pjax.js";}s:3:"css";a:0:{}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}s:32:"kartik\bs5dropdown\DropdownAsset";a:17:{s:10:"sourcePath";s:71:"C:\Web\test-project\vendor\kartik-v\yii2-bootstrap5-dropdown\src/assets";s:8:"basePath";s:48:"C:\Web\test-project\frontend\web\assets\74d8da42";s:7:"baseUrl";s:16:"/assets/74d8da42";s:7:"depends";a:3:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";i:2;s:35:"yii\bootstrap5\BootstrapPluginAsset";}s:2:"js";a:1:{i:0;s:14:"js/dropdown.js";}s:3:"css";a:1:{i:0;s:16:"css/dropdown.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}s:19:"bsDependencyEnabled";b:1;s:15:"bsPluginEnabled";b:1;s:9:"bsVersion";s:3:"5.x";s:16:"bsColCssPrefixes";a:0:{}s:9:" * _bsVer";i:5;s:21:" * _defaultIconPrefix";N;s:17:" * _defaultBtnCss";N;s:9:" * _isBs4";N;}s:24:"frontend\assets\AppAsset";a:9:{s:10:"sourcePath";N;s:8:"basePath";s:32:"C:\Web\test-project\frontend\web";s:7:"baseUrl";s:0:"";s:7:"depends";a:2:{i:0;s:16:"yii\web\YiiAsset";i:1;s:29:"yii\bootstrap5\BootstrapAsset";}s:2:"js";a:1:{i:0;s:10:"js/main.js";}s:3:"css";a:1:{i:0;s:12:"css/site.css";}s:9:"jsOptions";a:0:{}s:10:"cssOptions";a:0:{}s:14:"publishOptions";a:0:{}}}";s:7:"summary";a:13:{s:3:"tag";s:13:"68753db5927fd";s:3:"url";s:38:"http://localhost:9000/mock-posts/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:3:"::1";s:4:"time";d:1752513973.474883;s:10:"statusCode";i:200;s:8:"sqlCount";i:26;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:11866808;s:14:"processingTime";d:0.6263139247894287;}s:10:"exceptions";a:0:{}}