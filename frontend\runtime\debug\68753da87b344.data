a:14:{s:6:"config";s:12881:"a:5:{s:10:"phpVersion";s:5:"8.3.3";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.3.3";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:57:{s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:8:"2.0.50.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:54:"C:\Web\test-project\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:49:"C:\Web\test-project\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:47:"C:\Web\test-project\vendor/yiisoft/yii2-gii/src";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:57:"C:\Web\test-project\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"raoul2000/yii2-workflow";a:3:{s:4:"name";s:23:"raoul2000/yii2-workflow";s:7:"version";s:7:"1.2.0.0";s:5:"alias";a:1:{s:19:"@raoul2000/workflow";s:54:"C:\Web\test-project\vendor/raoul2000/yii2-workflow/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:7:"2.0.7.0";s:5:"alias";a:1:{s:8:"@yii/jui";s:43:"C:\Web\test-project\vendor/yiisoft/yii2-jui";}}s:28:"raoul2000/yii2-workflow-view";a:3:{s:4:"name";s:28:"raoul2000/yii2-workflow-view";s:7:"version";s:7:"0.0.2.0";s:5:"alias";a:1:{s:24:"@raoul2000/workflow/view";s:59:"C:\Web\test-project\vendor/raoul2000/yii2-workflow-view/src";}}s:32:"cornernote/yii2-workflow-manager";a:3:{s:4:"name";s:32:"cornernote/yii2-workflow-manager";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:28:"@cornernote/workflow/manager";s:63:"C:\Web\test-project\vendor/cornernote/yii2-workflow-manager/src";}}s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:7:"3.0.5.0";s:5:"alias";a:1:{s:12:"@kartik/base";s:56:"C:\Web\test-project\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:7:"1.0.6.0";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:7:"3.5.3.0";s:5:"alias";a:1:{s:12:"@kartik/grid";s:49:"C:\Web\test-project\vendor/kartik-v/yii2-grid/src";}}s:23:"kartik-v/yii2-popover-x";a:3:{s:4:"name";s:23:"kartik-v/yii2-popover-x";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:15:"@kartik/popover";s:54:"C:\Web\test-project\vendor/kartik-v/yii2-popover-x/src";}}s:33:"kartik-v/yii2-bootstrap5-dropdown";a:3:{s:4:"name";s:33:"kartik-v/yii2-bootstrap5-dropdown";s:7:"version";s:7:"1.0.2.0";s:5:"alias";a:1:{s:19:"@kartik/bs5dropdown";s:64:"C:\Web\test-project\vendor/kartik-v/yii2-bootstrap5-dropdown/src";}}s:26:"biladina/yii2-ajaxcrud-bs4";a:4:{s:4:"name";s:26:"biladina/yii2-ajaxcrud-bs4";s:7:"version";s:7:"3.0.0.0";s:5:"alias";a:1:{s:22:"@yii2ajaxcrud/ajaxcrud";s:57:"C:\Web\test-project\vendor/biladina/yii2-ajaxcrud-bs4/src";}s:9:"bootstrap";s:31:"yii2ajaxcrud\ajaxcrud\Bootstrap";}s:22:"kartik-v/yii2-editable";a:3:{s:4:"name";s:22:"kartik-v/yii2-editable";s:7:"version";s:7:"1.8.0.0";s:5:"alias";a:1:{s:16:"@kartik/editable";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-editable/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.12.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:54:"C:\Web\test-project\vendor/yiisoft/yii2-bootstrap4/src";}}s:29:"hail812/yii2-adminlte-widgets";a:3:{s:4:"name";s:29:"hail812/yii2-adminlte-widgets";s:7:"version";s:7:"1.0.5.0";s:5:"alias";a:1:{s:25:"@hail812/adminlte/widgets";s:60:"C:\Web\test-project\vendor/hail812/yii2-adminlte-widgets/src";}}s:22:"hail812/yii2-adminlte3";a:3:{s:4:"name";s:22:"hail812/yii2-adminlte3";s:7:"version";s:7:"1.1.9.0";s:5:"alias";a:1:{s:18:"@hail812/adminlte3";s:53:"C:\Web\test-project\vendor/hail812/yii2-adminlte3/src";}}s:31:"kartik-v/yii2-widget-activeform";a:3:{s:4:"name";s:31:"kartik-v/yii2-widget-activeform";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/form";s:62:"C:\Web\test-project\vendor/kartik-v/yii2-widget-activeform/src";}}s:18:"kartik-v/yii2-mpdf";a:3:{s:4:"name";s:18:"kartik-v/yii2-mpdf";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/mpdf";s:49:"C:\Web\test-project\vendor/kartik-v/yii2-mpdf/src";}}s:33:"dominus77/yii2-sweetalert2-widget";a:3:{s:4:"name";s:33:"dominus77/yii2-sweetalert2-widget";s:7:"version";s:7:"2.0.0.0";s:5:"alias";a:1:{s:22:"@dominus77/sweetalert2";s:64:"C:\Web\test-project\vendor/dominus77/yii2-sweetalert2-widget/src";}}s:28:"kartik-v/yii2-widget-select2";a:3:{s:4:"name";s:28:"kartik-v/yii2-widget-select2";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@kartik/select2";s:59:"C:\Web\test-project\vendor/kartik-v/yii2-widget-select2/src";}}s:28:"2amigos/yii2-ckeditor-widget";a:3:{s:4:"name";s:28:"2amigos/yii2-ckeditor-widget";s:7:"version";s:7:"2.1.0.0";s:5:"alias";a:1:{s:19:"@dosamigos/ckeditor";s:59:"C:\Web\test-project\vendor/2amigos/yii2-ckeditor-widget/src";}}s:17:"yiisoft/yii2-twig";a:3:{s:4:"name";s:17:"yiisoft/yii2-twig";s:7:"version";s:7:"2.5.1.0";s:5:"alias";a:1:{s:9:"@yii/twig";s:48:"C:\Web\test-project\vendor/yiisoft/yii2-twig/src";}}s:21:"kartik-v/yii2-helpers";a:3:{s:4:"name";s:21:"kartik-v/yii2-helpers";s:7:"version";s:7:"1.3.9.0";s:5:"alias";a:1:{s:15:"@kartik/helpers";s:52:"C:\Web\test-project\vendor/kartik-v/yii2-helpers/src";}}s:21:"kartik-v/yii2-builder";a:3:{s:4:"name";s:21:"kartik-v/yii2-builder";s:7:"version";s:7:"1.6.9.0";s:5:"alias";a:1:{s:15:"@kartik/builder";s:52:"C:\Web\test-project\vendor/kartik-v/yii2-builder/src";}}s:24:"kartik-v/yii2-checkbox-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-checkbox-x";s:7:"version";s:7:"1.0.7.0";s:5:"alias";a:1:{s:16:"@kartik/checkbox";s:55:"C:\Web\test-project\vendor/kartik-v/yii2-checkbox-x/src";}}s:26:"kartik-v/yii2-context-menu";a:3:{s:4:"name";s:26:"kartik-v/yii2-context-menu";s:7:"version";s:7:"1.2.4.0";s:5:"alias";a:1:{s:13:"@kartik/cmenu";s:57:"C:\Web\test-project\vendor/kartik-v/yii2-context-menu/src";}}s:24:"kartik-v/yii2-date-range";a:3:{s:4:"name";s:24:"kartik-v/yii2-date-range";s:7:"version";s:7:"1.7.3.0";s:5:"alias";a:1:{s:17:"@kartik/daterange";s:55:"C:\Web\test-project\vendor/kartik-v/yii2-date-range/src";}}s:25:"kartik-v/yii2-datecontrol";a:3:{s:4:"name";s:25:"kartik-v/yii2-datecontrol";s:7:"version";s:7:"1.9.9.0";s:5:"alias";a:1:{s:19:"@kartik/datecontrol";s:56:"C:\Web\test-project\vendor/kartik-v/yii2-datecontrol/src";}}s:25:"kartik-v/yii2-detail-view";a:3:{s:4:"name";s:25:"kartik-v/yii2-detail-view";s:7:"version";s:7:"1.8.7.0";s:5:"alias";a:1:{s:14:"@kartik/detail";s:56:"C:\Web\test-project\vendor/kartik-v/yii2-detail-view/src";}}s:22:"kartik-v/yii2-sortable";a:3:{s:4:"name";s:22:"kartik-v/yii2-sortable";s:7:"version";s:7:"1.2.2.0";s:5:"alias";a:1:{s:16:"@kartik/sortable";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-sortable/src";}}s:22:"kartik-v/yii2-dynagrid";a:3:{s:4:"name";s:22:"kartik-v/yii2-dynagrid";s:7:"version";s:7:"1.5.5.0";s:5:"alias";a:1:{s:16:"@kartik/dynagrid";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-dynagrid/src";}}s:20:"kartik-v/yii2-export";a:3:{s:4:"name";s:20:"kartik-v/yii2-export";s:7:"version";s:7:"1.4.3.0";s:5:"alias";a:1:{s:14:"@kartik/export";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-export/src";}}s:25:"kartik-v/yii2-field-range";a:3:{s:4:"name";s:25:"kartik-v/yii2-field-range";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:13:"@kartik/field";s:56:"C:\Web\test-project\vendor/kartik-v/yii2-field-range/src";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:8:"2.0.16.0";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:54:"C:\Web\test-project\vendor/yiisoft/yii2-httpclient/src";}}s:19:"kartik-v/yii2-icons";a:3:{s:4:"name";s:19:"kartik-v/yii2-icons";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/icons";s:50:"C:\Web\test-project\vendor/kartik-v/yii2-icons/src";}}s:27:"kartik-v/yii2-label-inplace";a:3:{s:4:"name";s:27:"kartik-v/yii2-label-inplace";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/label";s:58:"C:\Web\test-project\vendor/kartik-v/yii2-label-inplace/src";}}s:20:"kartik-v/yii2-ipinfo";a:3:{s:4:"name";s:20:"kartik-v/yii2-ipinfo";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:14:"@kartik/ipinfo";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-ipinfo/src";}}s:22:"kartik-v/yii2-markdown";a:3:{s:4:"name";s:22:"kartik-v/yii2-markdown";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/markdown";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-markdown/src";}}s:19:"kartik-v/yii2-money";a:3:{s:4:"name";s:19:"kartik-v/yii2-money";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:13:"@kartik/money";s:50:"C:\Web\test-project\vendor/kartik-v/yii2-money/src";}}s:24:"kartik-v/yii2-dropdown-x";a:3:{s:4:"name";s:24:"kartik-v/yii2-dropdown-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/dropdown";s:55:"C:\Web\test-project\vendor/kartik-v/yii2-dropdown-x/src";}}s:19:"kartik-v/yii2-nav-x";a:3:{s:4:"name";s:19:"kartik-v/yii2-nav-x";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:11:"@kartik/nav";s:50:"C:\Web\test-project\vendor/kartik-v/yii2-nav-x/src";}}s:22:"kartik-v/yii2-password";a:3:{s:4:"name";s:22:"kartik-v/yii2-password";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:16:"@kartik/password";s:53:"C:\Web\test-project\vendor/kartik-v/yii2-password/src";}}s:20:"kartik-v/yii2-slider";a:3:{s:4:"name";s:20:"kartik-v/yii2-slider";s:7:"version";s:7:"1.3.2.0";s:5:"alias";a:1:{s:14:"@kartik/slider";s:47:"C:\Web\test-project\vendor/kartik-v/yii2-slider";}}s:20:"kartik-v/yii2-social";a:3:{s:4:"name";s:20:"kartik-v/yii2-social";s:7:"version";s:7:"1.3.5.0";s:5:"alias";a:1:{s:14:"@kartik/social";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-social/src";}}s:28:"kartik-v/yii2-sortable-input";a:3:{s:4:"name";s:28:"kartik-v/yii2-sortable-input";s:7:"version";s:7:"*******";s:5:"alias";a:1:{s:17:"@kartik/sortinput";s:59:"C:\Web\test-project\vendor/kartik-v/yii2-sortable-input/src";}}s:20:"kartik-v/yii2-tabs-x";a:3:{s:4:"name";s:20:"kartik-v/yii2-tabs-x";s:7:"version";s:7:"1.2.9.0";s:5:"alias";a:1:{s:12:"@kartik/tabs";s:51:"C:\Web\test-project\vendor/kartik-v/yii2-tabs-x/src";}}s:26:"kartik-v/yii2-widget-growl";a:3:{s:4:"name";s:26:"kartik-v/yii2-widget-growl";s:7:"version";s:7:"1.1.2.0";s:5:"alias";a:1:{s:13:"@kartik/growl";s:57:"C:\Web\test-project\vendor/kartik-v/yii2-widget-growl/src";}}s:24:"kartik-v/yii2-validators";a:4:{s:4:"name";s:24:"kartik-v/yii2-validators";s:7:"version";s:7:"1.0.3.0";s:5:"alias";a:1:{s:18:"@kartik/validators";s:55:"C:\Web\test-project\vendor/kartik-v/yii2-validators/src";}s:9:"bootstrap";s:27:"kartik\validators\Bootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.27.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:49:"C:\Web\test-project\vendor/yiisoft/yii2-debug/src";}}s:32:"kartik-v/yii2-widget-switchinput";a:3:{s:4:"name";s:32:"kartik-v/yii2-widget-switchinput";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:19:"@kartik/switchinput";s:59:"C:\Web\test-project\vendor/kartik-v/yii2-widget-switchinput";}}s:18:"mdmsoft/yii2-admin";a:3:{s:4:"name";s:18:"mdmsoft/yii2-admin";s:7:"version";s:8:"2.12.0.0";s:5:"alias";a:1:{s:10:"@mdm/admin";s:45:"C:\Web\test-project\vendor/mdmsoft/yii2-admin";}}s:27:"2amigos/yii2-chartjs-widget";a:3:{s:4:"name";s:27:"2amigos/yii2-chartjs-widget";s:7:"version";s:7:"2.1.3.0";s:5:"alias";a:1:{s:18:"@dosamigos/chartjs";s:58:"C:\Web\test-project\vendor/2amigos/yii2-chartjs-widget/src";}}s:19:"bedezign/yii2-audit";a:4:{s:4:"name";s:19:"bedezign/yii2-audit";s:7:"version";s:7:"1.2.7.0";s:5:"alias";a:1:{s:20:"@bedezign/yii2/audit";s:50:"C:\Web\test-project\vendor/bedezign/yii2-audit/src";}s:9:"bootstrap";s:29:"bedezign\yii2\audit\Bootstrap";}s:25:"webzop/yii2-notifications";a:4:{s:4:"name";s:25:"webzop/yii2-notifications";s:7:"version";s:7:"0.2.0.0";s:5:"alias";a:1:{s:21:"@webzop/notifications";s:52:"C:\Web\test-project\vendor/webzop/yii2-notifications";}s:9:"bootstrap";s:30:"webzop\notifications\Bootstrap";}s:27:"tuyakhov/yii2-notifications";a:3:{s:4:"name";s:27:"tuyakhov/yii2-notifications";s:7:"version";s:7:"*******";s:5:"alias";a:2:{s:23:"@tuyakhov/notifications";s:58:"C:\Web\test-project\vendor/tuyakhov/yii2-notifications/src";s:29:"@tuyakhov/notifications/tests";s:60:"C:\Web\test-project\vendor/tuyakhov/yii2-notifications/tests";}}}}";s:3:"log";s:8120:"a:1:{s:8:"messages";a:24:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.391162;i:4;a:0:{}i:5;i:2795112;}i:1;a:6:{i:0;s:59:"Bootstrap with yii2ajaxcrud\ajaxcrud\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.391799;i:4;a:0:{}i:5;i:2827416;}i:2;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.391835;i:4;a:0:{}i:5;i:2828352;}i:3;a:6:{i:0;s:55:"Bootstrap with kartik\validators\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.392698;i:4;a:0:{}i:5;i:2861600;}i:4;a:6:{i:0;s:57:"Bootstrap with bedezign\yii2\audit\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.39343;i:4;a:0:{}i:5;i:2966904;}i:5;a:6:{i:0;s:58:"Bootstrap with webzop\notifications\Bootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.395232;i:4;a:0:{}i:5;i:3034640;}i:6;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.398874;i:4;a:0:{}i:5;i:3123856;}i:7;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.398924;i:4;a:0:{}i:5;i:3124496;}i:8;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.422025;i:4;a:0:{}i:5;i:4119336;}i:9;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.451736;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5440392;}i:10;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=test_project";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.451847;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5442512;}i:15;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.461604;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5499384;}i:18;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.463527;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5520648;}i:21;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.479103;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5970488;}i:24;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.503573;i:4;a:0:{}i:5;i:6680104;}i:25;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.511742;i:4;a:0:{}i:5;i:6939896;}i:34;a:6:{i:0;s:35:"Route requested: 'mock-posts/index'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.513563;i:4;a:0:{}i:5;i:6981832;}i:35;a:6:{i:0;s:30:"Route to run: mock-posts/index";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.516769;i:4;a:0:{}i:5;i:7097536;}i:36;a:6:{i:0;s:71:"Running action: frontend\controllers\MockPostsController::actionIndex()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.517499;i:4;a:0:{}i:5;i:7114864;}i:37;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `mock_posts`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.525606;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7281912;}i:40;a:6:{i:0;s:30:"SHOW CREATE TABLE `mock_posts`";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.528285;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7296688;}i:43;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'mock_posts' AND `kcu`.`TABLE_NAME` = 'mock_posts'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.529319;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7300336;}i:46;a:6:{i:0;s:130:"GET http://localhost:9000/api/sync?synced=0
Authorization: Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO";i:1;i:4;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:**********.54113;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:87;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:107;s:8:"function";s:13:"fetchSyncData";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7519256;}i:49;a:6:{i:0;s:41:"SELECT EXISTS(SELECT * FROM `mock_posts`)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.671574;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:110;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:7576656;}}}";s:9:"profiling";s:11701:"a:3:{s:6:"memory";i:8698432;s:4:"time";d:0.****************;s:8:"messages";a:20:{i:11;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=test_project";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.451887;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5443320;}i:12;a:6:{i:0;s:63:"Opening DB connection: mysql:host=localhost;dbname=test_project";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.457184;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5486624;}i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.457251;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5486408;}i:14;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.461475;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5498096;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.46164;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5500296;}i:17;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.462754;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5502936;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.463549;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5521688;}i:20;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.466045;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5524216;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.479172;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5970872;}i:23;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.48024;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5973216;}i:38;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `mock_posts`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.525682;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7283200;}i:39;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `mock_posts`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.528227;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7295024;}i:41;a:6:{i:0;s:30:"SHOW CREATE TABLE `mock_posts`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.528299;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7297976;}i:42;a:6:{i:0;s:30:"SHOW CREATE TABLE `mock_posts`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.528981;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7300264;}i:44;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'mock_posts' AND `kcu`.`TABLE_NAME` = 'mock_posts'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.52933;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7301752;}i:45;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'mock_posts' AND `kcu`.`TABLE_NAME` = 'mock_posts'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.53083;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7303984;}i:47;a:6:{i:0;s:130:"GET http://localhost:9000/api/sync?synced=0
Authorization: Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO";i:1;i:80;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:**********.54118;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:87;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:107;s:8:"function";s:13:"fetchSyncData";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7520440;}i:48;a:6:{i:0;s:130:"GET http://localhost:9000/api/sync?synced=0
Authorization: Bearer lB9XAvqRSFNU6SYrSzImapPjfP_1xx70DNGvMQSrGGVHiEWEiyz7QwNqw3o8PvBO";i:1;i:96;i:2;s:36:"yii\httpclient\StreamTransport::send";i:3;d:**********.66719;i:4;a:2:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:87;s:8:"function";s:4:"send";s:5:"class";s:22:"yii\httpclient\Request";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:107;s:8:"function";s:13:"fetchSyncData";s:5:"class";s:40:"frontend\controllers\MockPostsController";s:4:"type";s:2:"->";}}i:5;i:7524320;}i:50;a:6:{i:0;s:41:"SELECT EXISTS(SELECT * FROM `mock_posts`)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.671623;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:110;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:7577568;}i:51;a:6:{i:0;s:41:"SELECT EXISTS(SELECT * FROM `mock_posts`)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.673354;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:110;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:7578464;}}}";s:2:"db";s:9570:"a:1:{s:8:"messages";a:16:{i:13;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.457251;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5486408;}i:14;a:6:{i:0;s:29:"SHOW FULL COLUMNS FROM `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.461475;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5498096;}i:16;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.46164;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5500296;}i:17;a:6:{i:0;s:24:"SHOW CREATE TABLE `user`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.462754;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5502936;}i:19;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.463549;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5521688;}i:20;a:6:{i:0;s:764:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'user' AND `kcu`.`TABLE_NAME` = 'user'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.466045;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5524216;}i:22;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.479172;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5970872;}i:23;a:6:{i:0;s:53:"SELECT * FROM `user` WHERE (`id`=1) AND (`status`=10)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.48024;i:4;a:1:{i:0;a:5:{s:4:"file";s:42:"C:\Web\test-project\common\models\User.php";s:4:"line";i:67;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}}i:5;i:5973216;}i:38;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `mock_posts`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.525682;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7283200;}i:39;a:6:{i:0;s:35:"SHOW FULL COLUMNS FROM `mock_posts`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.528227;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7295024;}i:41;a:6:{i:0;s:30:"SHOW CREATE TABLE `mock_posts`";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.528299;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7297976;}i:42;a:6:{i:0;s:30:"SHOW CREATE TABLE `mock_posts`";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.528981;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7300264;}i:44;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'mock_posts' AND `kcu`.`TABLE_NAME` = 'mock_posts'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.52933;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7301752;}i:45;a:6:{i:0;s:776:"SELECT
    `kcu`.`CONSTRAINT_NAME` AS `constraint_name`,
    `kcu`.`COLUMN_NAME` AS `column_name`,
    `kcu`.`REFERENCED_TABLE_NAME` AS `referenced_table_name`,
    `kcu`.`REFERENCED_COLUMN_NAME` AS `referenced_column_name`
FROM `information_schema`.`REFERENTIAL_CONSTRAINTS` AS `rc`
JOIN `information_schema`.`KEY_COLUMN_USAGE` AS `kcu` ON
    (
        `kcu`.`CONSTRAINT_CATALOG` = `rc`.`CONSTRAINT_CATALOG` OR
        (`kcu`.`CONSTRAINT_CATALOG` IS NULL AND `rc`.`CONSTRAINT_CATALOG` IS NULL)
    ) AND
    `kcu`.`CONSTRAINT_SCHEMA` = `rc`.`CONSTRAINT_SCHEMA` AND
    `kcu`.`CONSTRAINT_NAME` = `rc`.`CONSTRAINT_NAME`
WHERE `rc`.`CONSTRAINT_SCHEMA` = database() AND `kcu`.`TABLE_SCHEMA` = database()
AND `rc`.`TABLE_NAME` = 'mock_posts' AND `kcu`.`TABLE_NAME` = 'mock_posts'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.53083;i:4;a:2:{i:0;a:5:{s:4:"file";s:60:"C:\Web\test-project\common\models\search\MockPostsSearch.php";s:4:"line";i:52;s:8:"function";s:8:"validate";s:5:"class";s:14:"yii\base\Model";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:104;s:8:"function";s:6:"search";s:5:"class";s:36:"common\models\search\MockPostsSearch";s:4:"type";s:2:"->";}}i:5;i:7303984;}i:50;a:6:{i:0;s:41:"SELECT EXISTS(SELECT * FROM `mock_posts`)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.671623;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:110;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:7577568;}i:51;a:6:{i:0;s:41:"SELECT EXISTS(SELECT * FROM `mock_posts`)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.673354;i:4;a:1:{i:0;a:5:{s:4:"file";s:64:"C:\Web\test-project\frontend\controllers\MockPostsController.php";s:4:"line";i:110;s:8:"function";s:6:"exists";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}}i:5;i:7578464;}}}";s:5:"event";s:3062:"a:17:{i:0;a:5:{s:4:"time";d:**********.444294;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:1;a:5:{s:4:"time";d:**********.457163;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:2;a:5:{s:4:"time";d:**********.482049;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:3;a:5:{s:4:"time";d:**********.482169;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:4;a:5:{s:4:"time";d:**********.496554;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"common\models\User";}i:5;a:5:{s:4:"time";d:**********.512478;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:6;a:5:{s:4:"time";d:**********.517157;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:7;a:5:{s:4:"time";d:**********.517472;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:40:"frontend\controllers\MockPostsController";}i:8;a:5:{s:4:"time";d:**********.518959;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"common\models\search\MockPostsSearch";}i:9;a:5:{s:4:"time";d:**********.519038;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:10;a:5:{s:4:"time";d:**********.522179;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"common\models\search\MockPostsSearch";}i:11;a:5:{s:4:"time";d:**********.530894;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:36:"common\models\search\MockPostsSearch";}i:12;a:5:{s:4:"time";d:**********.538824;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:13;a:5:{s:4:"time";d:**********.538893;s:4:"name";s:10:"beforeSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:14;a:5:{s:4:"time";d:**********.668417;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:21:"yii\httpclient\Client";}i:15;a:5:{s:4:"time";d:**********.668438;s:4:"name";s:9:"afterSend";s:5:"class";s:27:"yii\httpclient\RequestEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:22:"yii\httpclient\Request";}i:16;a:5:{s:4:"time";d:**********.671414;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.382737;s:3:"end";d:**********.912505;s:6:"memory";i:8698432;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:1668:"a:3:{s:8:"messages";a:8:{i:26;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.513403;i:4;a:0:{}i:5;i:6977728;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.513434;i:4;a:0:{}i:5;i:6978320;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.513452;i:4;a:0:{}i:5;i:6978912;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.513468;i:4;a:0:{}i:5;i:6979504;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.513482;i:4;a:0:{}i:5;i:6980096;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:26:"GET api/<resource:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.513514;i:4;a:0:{}i:5;i:6980744;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:26:"PUT api/<resource:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.513532;i:4;a:0:{}i:5;i:6981392;}i:33;a:6:{i:0;s:55:"No matching URL rules. Using default URL parsing logic.";i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.513542;i:4;a:0:{}i:5;i:6982248;}}s:5:"route";s:16:"mock-posts/index";s:6:"action";s:55:"frontend\controllers\MockPostsController::actionIndex()";}";s:7:"request";s:10369:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:20:{s:12:"content-type";s:0:"";s:14:"content-length";s:1:"0";s:14:"x-original-url";s:17:"/mock-posts/index";s:14:"sec-fetch-dest";s:8:"document";s:14:"sec-fetch-user";s:2:"?1";s:14:"sec-fetch-mode";s:8:"navigate";s:14:"sec-fetch-site";s:11:"same-origin";s:25:"upgrade-insecure-requests";s:1:"1";s:18:"sec-ch-ua-platform";s:9:""Windows"";s:16:"sec-ch-ua-mobile";s:2:"?0";s:9:"sec-ch-ua";s:64:""Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"";s:10:"user-agent";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:7:"referer";s:38:"http://localhost:9000/mock-posts/index";s:4:"host";s:14:"localhost:9000";s:6:"cookie";s:954:"_identity-frontend=1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; advanced-frontend=lj75dob3daup08nk4a8tootgbt; _csrf-frontend=987794102ea9939de322b56534be5c83dc38b85b3c5875dcd6a75e6555987485a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22mGPFQ_JwvodttQ9_4GPme5P-ekoMqTgH%22%3B%7D; advanced-backend-test=0e3n9oj5c286gmtljdrc2j5hu5; _csrf-backend=662260930e32dfab483a7cb4ebeea2fc94ee5f0fd8822614b5c1202ccdd85223a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22uC8kUI6_jtEX6ckmjU3H4rhhguwLkXAD%22%3B%7D";s:15:"accept-language";s:14:"en-US,en;q=0.9";s:15:"accept-encoding";s:23:"gzip, deflate, br, zstd";s:6:"accept";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:10:"connection";s:10:"keep-alive";s:13:"cache-control";s:9:"max-age=0";}s:15:"responseHeaders";a:5:{s:12:"X-Powered-By";s:9:"PHP/8.3.3";s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:12:"Content-type";s:24:"text/html; charset=UTF-8";}s:5:"route";s:16:"mock-posts/index";s:6:"action";s:55:"frontend\controllers\MockPostsController::actionIndex()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:3:"GET";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:108:{s:13:"_FCGI_X_PIPE_";s:53:"\\.\pipe\IISFCGI-164134d3-e58a-4807-90b8-58fadc75ad36";s:5:"PHPRC";s:26:"C:\Program Files\PHP\v8.3\";s:21:"PHP_FCGI_MAX_REQUESTS";s:5:"10000";s:15:"ALLUSERSPROFILE";s:14:"C:\ProgramData";s:7:"APPDATA";s:56:"C:\WINDOWS\system32\config\systemprofile\AppData\Roaming";s:15:"APP_POOL_CONFIG";s:57:"C:\inetpub\temp\apppools\Test Project\Test Project.config";s:11:"APP_POOL_ID";s:12:"Test Project";s:18:"CommonProgramFiles";s:29:"C:\Program Files\Common Files";s:23:"CommonProgramFiles(x86)";s:35:"C:\Program Files (x86)\Common Files";s:18:"CommonProgramW6432";s:29:"C:\Program Files\Common Files";s:12:"COMPUTERNAME";s:10:"EGOV-L-046";s:7:"ComSpec";s:27:"C:\WINDOWS\system32\cmd.exe";s:10:"DriverData";s:38:"C:\Windows\System32\Drivers\DriverData";s:10:"IGCCSVC_DB";s:416:"AQAAANCMnd8BFdERjHoAwE/Cl+sBAAAAxsjL008UHEujcYXte9n1xAQAAAACAAAAAAAQZgAAAAEAACAAAACG6V8fH1MiQpt+vTHr85FUmGEpkihjwnphUZ8EBmjXIgAAAAAOgAAAAAIAACAAAAAwiEFpINOfvm27eQP7A6KJKkU7BiIp8dfO5XJb9O9V82AAAABOuhJMP+CaR040CClff82PsaeGd4XybaOdhylSvqlmGruwc3EJIvD6UYcnWqB0s7SJb8fFsrKIKR8cZ/eXSEmMUiHF6J1xrcv30HL80qkrgc0BXBLoS19xuBSzihq7gPZAAAAABjLonwgGXzz5g0gBw2ytpgnFnasAK0EsnQP6jk1iL/gtzNQgs4cOrzDs1Y9Qn4IMfeSQ9eZqer9cZQRh9WmStg==";s:12:"LOCALAPPDATA";s:54:"C:\WINDOWS\system32\config\systemprofile\AppData\Local";s:20:"NUMBER_OF_PROCESSORS";s:2:"12";s:14:"OnlineServices";s:15:"Online Services";s:2:"OS";s:10:"Windows_NT";s:4:"Path";s:462:"C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\PHP\v8.3;C:\ProgramData\ComposerSetup\bin;C:\ProgramData\ComposerSetup\bin\composer.bat;C:\Program Files (x86)\HP\HP OCR\DB_Lib\;C:\Program Files\HP\Common\HPDestPlgIn\;C:\Program Files (x86)\HP\Common\HPDestPlgIn\;C:\WINDOWS\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps";s:7:"PATHEXT";s:53:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC";s:12:"platformcode";s:2:"AN";s:22:"PROCESSOR_ARCHITECTURE";s:5:"AMD64";s:20:"PROCESSOR_IDENTIFIER";s:51:"Intel64 Family 6 Model 186 Stepping 3, GenuineIntel";s:15:"PROCESSOR_LEVEL";s:1:"6";s:18:"PROCESSOR_REVISION";s:4:"ba03";s:11:"ProgramData";s:14:"C:\ProgramData";s:12:"ProgramFiles";s:16:"C:\Program Files";s:17:"ProgramFiles(x86)";s:22:"C:\Program Files (x86)";s:12:"ProgramW6432";s:16:"C:\Program Files";s:12:"PSModulePath";s:93:"C:\Program Files\WindowsPowerShell\Modules;C:\WINDOWS\system32\WindowsPowerShell\v1.0\Modules";s:6:"PUBLIC";s:15:"C:\Users\<USER>\WINDOWS";s:4:"TEMP";s:15:"C:\WINDOWS\TEMP";s:3:"TMP";s:15:"C:\WINDOWS\TEMP";s:10:"USERDOMAIN";s:3:"GOV";s:8:"USERNAME";s:11:"EGOV-L-046$";s:11:"USERPROFILE";s:40:"C:\WINDOWS\system32\config\systemprofile";s:6:"windir";s:10:"C:\WINDOWS";s:17:"ZES_ENABLE_SYSMAN";s:1:"1";s:14:"ORIG_PATH_INFO";s:10:"/index.php";s:3:"URL";s:10:"/index.php";s:15:"SERVER_SOFTWARE";s:18:"Microsoft-IIS/10.0";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:18:"SERVER_PORT_SECURE";s:1:"0";s:11:"SERVER_PORT";s:4:"9000";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SCRIPT_NAME";s:10:"/index.php";s:15:"SCRIPT_FILENAME";s:42:"C:\Web\test-project\frontend\web\index.php";s:11:"REQUEST_URI";s:17:"/mock-posts/index";s:14:"REQUEST_METHOD";s:3:"GET";s:11:"REMOTE_USER";s:0:"";s:11:"REMOTE_PORT";s:5:"49684";s:11:"REMOTE_HOST";s:3:"::1";s:11:"REMOTE_ADDR";s:3:"::1";s:12:"QUERY_STRING";s:0:"";s:15:"PATH_TRANSLATED";s:42:"C:\Web\test-project\frontend\web\index.php";s:10:"LOGON_USER";s:0:"";s:10:"LOCAL_ADDR";s:3:"::1";s:18:"INSTANCE_META_PATH";s:11:"/LM/W3SVC/4";s:13:"INSTANCE_NAME";s:12:"TEST PROJECT";s:11:"INSTANCE_ID";s:1:"4";s:20:"HTTPS_SERVER_SUBJECT";s:0:"";s:19:"HTTPS_SERVER_ISSUER";s:0:"";s:19:"HTTPS_SECRETKEYSIZE";s:0:"";s:13:"HTTPS_KEYSIZE";s:0:"";s:5:"HTTPS";s:3:"off";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:13:"DOCUMENT_ROOT";s:32:"C:\Web\test-project\frontend\web";s:12:"CONTENT_TYPE";s:0:"";s:14:"CONTENT_LENGTH";s:1:"0";s:12:"CERT_SUBJECT";s:0:"";s:17:"CERT_SERIALNUMBER";s:0:"";s:11:"CERT_ISSUER";s:0:"";s:10:"CERT_FLAGS";s:0:"";s:11:"CERT_COOKIE";s:0:"";s:9:"AUTH_USER";s:0:"";s:13:"AUTH_PASSWORD";s:0:"";s:9:"AUTH_TYPE";s:0:"";s:18:"APPL_PHYSICAL_PATH";s:33:"C:\Web\test-project\frontend\web\";s:12:"APPL_MD_PATH";s:16:"/LM/W3SVC/4/ROOT";s:20:"IIS_UrlRewriteModule";s:13:"7,1,1993,2351";s:13:"UNENCODED_URL";s:17:"/mock-posts/index";s:19:"IIS_WasUrlRewritten";s:1:"1";s:19:"HTTP_X_ORIGINAL_URL";s:17:"/mock-posts/index";s:19:"HTTP_SEC_FETCH_DEST";s:8:"document";s:19:"HTTP_SEC_FETCH_USER";s:2:"?1";s:19:"HTTP_SEC_FETCH_MODE";s:8:"navigate";s:19:"HTTP_SEC_FETCH_SITE";s:11:"same-origin";s:30:"HTTP_UPGRADE_INSECURE_REQUESTS";s:1:"1";s:23:"HTTP_SEC_CH_UA_PLATFORM";s:9:""Windows"";s:21:"HTTP_SEC_CH_UA_MOBILE";s:2:"?0";s:14:"HTTP_SEC_CH_UA";s:64:""Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"";s:15:"HTTP_USER_AGENT";s:111:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";s:12:"HTTP_REFERER";s:38:"http://localhost:9000/mock-posts/index";s:9:"HTTP_HOST";s:14:"localhost:9000";s:11:"HTTP_COOKIE";s:954:"_identity-frontend=1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca%3A2%3A%7Bi%3A0%3Bs%3A18%3A%22_identity-frontend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; sidebar-collapse=false; _identity-backend=946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa%3A2%3A%7Bi%3A0%3Bs%3A17%3A%22_identity-backend%22%3Bi%3A1%3Bs%3A46%3A%22%5B1%2C%22RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo%22%2C2592000%5D%22%3B%7D; advanced-frontend=lj75dob3daup08nk4a8tootgbt; _csrf-frontend=987794102ea9939de322b56534be5c83dc38b85b3c5875dcd6a75e6555987485a%3A2%3A%7Bi%3A0%3Bs%3A14%3A%22_csrf-frontend%22%3Bi%3A1%3Bs%3A32%3A%22mGPFQ_JwvodttQ9_4GPme5P-ekoMqTgH%22%3B%7D; advanced-backend-test=0e3n9oj5c286gmtljdrc2j5hu5; _csrf-backend=662260930e32dfab483a7cb4ebeea2fc94ee5f0fd8822614b5c1202ccdd85223a%3A2%3A%7Bi%3A0%3Bs%3A13%3A%22_csrf-backend%22%3Bi%3A1%3Bs%3A32%3A%22uC8kUI6_jtEX6ckmjU3H4rhhguwLkXAD%22%3B%7D";s:20:"HTTP_ACCEPT_LANGUAGE";s:14:"en-US,en;q=0.9";s:20:"HTTP_ACCEPT_ENCODING";s:23:"gzip, deflate, br, zstd";s:11:"HTTP_ACCEPT";s:135:"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7";s:19:"HTTP_CONTENT_LENGTH";s:1:"0";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:18:"HTTP_CACHE_CONTROL";s:9:"max-age=0";s:9:"FCGI_ROLE";s:9:"RESPONDER";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.360885;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:7:{s:18:"_identity-frontend";s:158:"1b6faf82f1373e7b344d26705d9b9116af70be560aed350448306ef136ca57aca:2:{i:0;s:18:"_identity-frontend";i:1;s:46:"[1,"RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo",2592000]";}";s:16:"sidebar-collapse";s:5:"false";s:17:"_identity-backend";s:157:"946df6687b5fb74a9b0eaa13756e2019f90ea1fbcefd9b8ca4919bcb5352521aa:2:{i:0;s:17:"_identity-backend";i:1;s:46:"[1,"RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo",2592000]";}";s:17:"advanced-frontend";s:26:"lj75dob3daup08nk4a8tootgbt";s:14:"_csrf-frontend";s:140:"987794102ea9939de322b56534be5c83dc38b85b3c5875dcd6a75e6555987485a:2:{i:0;s:14:"_csrf-frontend";i:1;s:32:"mGPFQ_JwvodttQ9_4GPme5P-ekoMqTgH";}";s:21:"advanced-backend-test";s:26:"0e3n9oj5c286gmtljdrc2j5hu5";s:13:"_csrf-backend";s:139:"662260930e32dfab483a7cb4ebeea2fc94ee5f0fd8822614b5c1202ccdd85223a:2:{i:0;s:13:"_csrf-backend";i:1;s:32:"uC8kUI6_jtEX6ckmjU3H4rhhguwLkXAD";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:3:{s:7:"__flash";a:0:{}s:4:"__id";i:1;s:9:"__authKey";s:32:"RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo";}}";s:4:"user";s:2311:"a:5:{s:2:"id";i:1;s:8:"identity";a:11:{s:2:"id";s:1:"1";s:8:"username";s:8:"'qwerty'";s:8:"auth_key";s:34:"'RqFKSasRjFLubQlLa-pnGlA3EFeIc9bo'";s:13:"password_hash";s:62:"'$2y$13$PvAUhC2Ak1Dg55cbPHYsDu8KTgzGOeu08y2SUHvEoYbw7Xqvr8wMG'";s:20:"password_reset_token";s:4:"null";s:5:"email";s:17:"'<EMAIL>'";s:6:"status";s:2:"10";s:10:"created_at";s:10:"1746551344";s:10:"updated_at";s:10:"1746551344";s:18:"verification_token";s:45:"'HyyhnMnjystP5EIGPwdessQ_HqW8DtaX_1746551344'";s:7:"role_id";s:1:"1";}s:10:"attributes";a:11:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:13:"Password Hash";}i:4;a:2:{s:9:"attribute";s:20:"password_reset_token";s:5:"label";s:20:"Password Reset Token";}i:5;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:18:"verification_token";s:5:"label";s:18:"Verification Token";}i:10;a:2:{s:9:"attribute";s:7:"role_id";s:5:"label";s:7:"Role Id";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-2";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"68753da87b344";s:3:"url";s:38:"http://localhost:9000/mock-posts/index";s:4:"ajax";i:0;s:6:"method";s:3:"GET";s:2:"ip";s:3:"::1";s:4:"time";d:**********.360885;s:10:"statusCode";i:200;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:8698432;s:14:"processingTime";d:0.****************;}s:10:"exceptions";a:0:{}}